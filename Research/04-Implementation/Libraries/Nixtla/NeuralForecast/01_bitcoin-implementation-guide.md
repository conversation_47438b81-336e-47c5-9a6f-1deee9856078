---
title: "Bitcoin Price Forecasting with NeuralForecast Implementation Guide"
permalink: "implementation/libraries/nixtla/neuralforecast/bitcoin-implementation-guide"
type: "technical-guide"
created: "2025-05-24"
last_updated: "2025-05-24"
tags:
  - bitcoin
  - neuralforecast
  - patchtst
  - cryptocurrency
  - deep-learning
  - nixtla
  - implementation
models:
  - PatchTST
  - LSTM
  - GRU
  - TFT
  - DeepAR
  - TCN
  - NHITS
techniques:
  - neural-forecasting
  - feature-engineering
  - loss-optimization
  - model-configuration
  - robust-scaling
  - walk-forward-validation
datasets:
  - "Bitcoin intraday"
  - "Bitcoin OHLCV"
complexity: "intermediate"
summary: "Comprehensive implementation guide for Bitcoin price forecasting using Nixtla's NeuralForecast library, covering PatchTST configuration, alternative models, robust losses, and feature engineering for volatile cryptocurrency data"
related:
  - "implementation/libraries/nixtla/neuralforecast/bitcoin-research-comprehensive"
  - "domain-applications/cryptocurrency/bitcoin/intraday-forecasting-guide"
  - "domain-applications/cryptocurrency/bitcoin/bitcoin-forecasting-advanced"
  - "case-studies/benchmarks/nixtla-model-comparison-benchmark"
  - "techniques/ensembling"
libraries:
  - "NeuralForecast"
  - "PyTorch"
  - "PyTorch Lightning"
---

# Bitcoin Price Forecasting with Nixtla's NeuralForecast

## 1. NeuralForecast Architecture & Data Handling

Nixtla's **NeuralForecast** library uses a *global model* paradigm: one model can learn across multiple time series (improving generalization as seen in M4 competitions), though it also works for a single series. The input dataset must be a pandas DataFrame in **long format** with three columns: `unique_id`, `ds` (timestamp), and `y` (target value). For a single series like Bitcoin, you can set `unique_id` to a constant (e.g. `"BTC"` for all rows). Ensure the timestamps `ds` are hourly datetimes to match the 1H frequency.

**Data Loading:** 120+ days of hourly Bitcoin prices (\~2880 data points) easily fit in memory. You can load this into a DataFrame and add the required columns:

```python
import pandas as pd
# Assume `prices` is a list or series of hourly BTC prices and `dates` is a DateTime index
df = pd.DataFrame({'ds': dates, 'y': prices})
df['unique_id'] = 'BTC'
df = df[['unique_id','ds','y']]  # reorder columns
```

NeuralForecast will internally construct training **windows** from this time series, rather than training on the full sequence at once. It automatically creates overlapping sub-sequences (input windows and corresponding forecast targets) to feed the model, which helps with memory and randomization during training. You don't have to manually generate these windows – the framework's DataLoader handles it.

For **multiple time series or very large data**, Nixtla offers a memory-efficient loader: you can store each series in a separate Parquet file and use the *large-scale DataLoader* that loads one batch at a time. This way, even datasets that don't fit entirely in RAM can be processed in batches (only one batch resides in memory at a time). The files should be sorted by time and placed under directories named `unique_id=<series_id>`. This custom loader is especially useful if you decide to train on many assets or long histories beyond a few million rows.

**Batching and Memory:** NeuralForecast's default DataLoader will batch multiple time-series windows together (by default batch\_size=32) for training. On an 8GB GPU, a batch of \~32 windows (with reasonable window length) is usually safe. If memory is constrained, you can reduce the `batch_size` or limit the number of windows per batch (Nixtla models have parameters like `windows_batch_size` and separate `valid_batch_size` to manage training vs validation memory). Also consider converting data to smaller dtypes (e.g., `float32`) to save memory.

If you need custom handling (for instance, generating windows with certain constraints), you could implement a custom PyTorch `Dataset` and DataLoader. However, in most cases the built-in loader suffices. The Nixtla DataLoader supports **recurrent windows** and **multivariate windows** under the hood, so it can create sequences appropriate for RNNs or transformers as needed. The key is to provide the data in the required format and specify the frequency (e.g. `freq='H'` for hourly) when initializing `NeuralForecast`.

**GPU memory optimization:** When training on GPU, keep an eye on memory usage. Techniques to reduce GPU load include: using smaller batch sizes, truncating very long input sequences, and disabling unnecessary logging (to avoid storing too many tensors for backprop graphs). If you have multiple series, the *global model* will learn from all simultaneously – this is efficient, but ensure you shuffle or scale the data so that no single series dominates the gradients. NeuralForecast automatically applies scaling if specified (see **Time Series Scaling** below), which helps keep different series on comparable scales.

Finally, for a single series like BTC, you won't benefit from cross-learning, but you **must still include** the `unique_id` column (set to a constant) to satisfy the input format. The framework will treat it as a degenerate global model with one series. In summary, prepare a DataFrame with columns `['unique_id','ds','y']`, and NeuralForecast will handle slicing it into training windows and feeding to the neural model.

## 2. PatchTST Model Configuration for Hourly Bitcoin Data

For long-horizon forecasting on high-frequency data, **PatchTST** is a strong model choice. PatchTST is a Transformer-based architecture that *patches* the time series into segments fed as input tokens, and employs **channel independence** (each target or feature is processed in its own transformer branch). This design is memory-efficient and effective for long sequences. Key configuration aspects for using PatchTST on hourly BTC data include the patch length, normalization, and training hyperparameters:

* **Patch length (`patch_len`) and stride:** PatchTST segments the input window into patches of length `patch_len` (with a sliding window stride between patches). By default, Nixtla sets `patch_len=32` and `stride=16`. For hourly data with daily seasonality, you might adjust `patch_len` to 24 (exactly one day) or 48 (two days) to better capture daily cycles. A larger patch covers longer patterns per token (reducing number of tokens), while a smaller patch yields more tokens and fine-grained detail. The `stride` controls patch overlap; e.g. `stride=16` on `patch_len=32` overlaps patches by 50%. Using `patch_len=24, stride=24` would take non-overlapping daily patches. In practice, a bit of overlap is beneficial to share info between patches. **Impact:** Increasing `patch_len` (up to the input window length) will reduce the number of tokens (thus slightly less self-attention cost) but each token represents a longer subseries. Overlap (`stride < patch_len`) increases the token count and training cost but can improve performance by not missing boundary information. As a rule of thumb, start with the defaults (32/16) and consider tuning: for BTC, if daily seasonality is important, try `patch_len=24, stride=12 or 24` and evaluate. The library will internally ensure `patch_len <= input_size` (if your input window is shorter than patch\_len, it adjusts it).

* **Input window (`input_size`) and horizon (`h`):** PatchTST (and other models) require specifying the forecast horizon `h` (how many future time steps to predict) and the length of past data to use as input (`input_size`). A common practice is to set `input_size` to cover at least one or two seasonal cycles of the data. For example, if you forecast 24 hours ahead (`h=24`), you might use `input_size=168` (the past 7 days) so that weekly patterns can be captured. If data shows longer-term trends, consider even larger windows (e.g. 30 days = 720 hours) as long as memory allows. Keep in mind a larger `input_size` means more patches/tokens, increasing training time and GPU memory usage (attention cost grows roughly with input length). **BTC's patterns:** Typically include daily cycles and potentially weekly cycles in trading activity; setting `input_size` to 168 hours (1 week) or 336 (2 weeks) is a good starting point. PatchTST can handle these lengths thanks to patching (which reduces effective sequence length). Just ensure `input_size` is significantly greater than `patch_len` so multiple patches are created.

* **RevIN normalization:** PatchTST in NeuralForecast supports **RevIN (Reversible Instance Normalization)**, a technique that normalizes each sample (each time-series window) by its mean and variance and then inverts that transformation on the model output. By default `revin=True` in PatchTST, meaning RevIN is applied. This is very useful for volatile financial data: RevIN will stabilize the scale of each training window (so the model isn't thrown off by price level shifts over time) and then reverse the normalization to output in original scale. For example, if Bitcoin's price level doubles over a month, RevIN ensures each window the model sees is mean-centered, so the model learns on de-trended values, reducing non-stationarity. We recommend keeping `revin=True` for BTC. The model has additional RevIN options: `revin_affine` (whether to learn a scale/shift during normalization) and `revin_subtract_last` (whether to subtract the last value rather than the mean for normalization). In Nixtla's implementation, `revin_affine=False` and `revin_subtract_last=False` by default. This means by default RevIN will subtract the window mean and divide by std, without any learned affine transform. You can experiment with `revin_subtract_last=True` – that would normalize each window by subtracting the last value (essentially de-anchoring the series at the end), which can sometimes help if the series has a strong local trend. However, for most cases, the standard RevIN (subtract mean) is effective at making the series stationary.

* **Scaling (robust vs standard):** In addition to RevIN, NeuralForecast can apply **window scaling** to inputs via the `scaler_type` parameter of each model. The *robust scaler* (median and interquartile range) is the default for many models and is well-suited for Bitcoin. A robust scaler will normalize each batch/window by subtracting the median and dividing by the IQR, which is resilient to extreme outliers. By contrast, a standard scaler (mean and standard deviation) might be skewed by large price spikes. For PatchTST, Nixtla often uses `scaler_type='robust'` to complement RevIN. Essentially, RevIN handles instance-wise scale shifts, while `scaler_type` ensures each window's distribution is scaled in a consistent way for network training. We suggest using `scaler_type='robust'` (especially if not using log returns; BTC's price jumps can be extreme outliers). If you did log-transform the prices (discussed in section 9), a standard scaler could suffice, but robust scaling is still a safe choice. Note that `scaler_type` applies at **each training window** (akin to temporal normalization), whereas *local scaling* (via `local_scaler_type` in the NeuralForecast core) would scale each entire time series once. For a single series, local scaling just scales the whole series and may be less important if you use RevIN+robust internally. In summary: enable robust scaling and RevIN for stability – the combination means the model sees each window roughly standardized, mitigating volatility and trend changes.

* **Learning rate scheduling:** Financial series are noisy, so a good learning rate schedule can improve training stability. By default, NeuralForecast uses a simple **StepLR** scheduler (learning rate drops a fixed factor a few times during training) if not overridden. Specifically, models have `num_lr_decays` (e.g. 3) and will decay the LR evenly over `max_steps`. For example, with `max_steps=1000` and `num_lr_decays=3`, the LR will drop at steps \~333, \~666, \~1000 by default. This is a reasonable strategy, but for BTC you might consider more sophisticated schedules:

  * **Cyclic or cosine schedules:** A **cosine annealing** schedule with warm restarts can periodically reduce and increase the LR, which sometimes helps escape local minima in non-stationary data. For a volatile asset, a **cyclical learning rate** (e.g. the One-Cycle policy) can also work: start low, peak mid-training, and then anneal. These schedules allow the model to make big jumps in parameter space early (when trying to capture general patterns), and fine-tune later. PyTorch Lightning (which NeuralForecast is built on) supports these – you can supply a custom `lr_scheduler` when instantiating the model.
  * **Reduce on plateau:** An alternative is monitoring validation loss and reducing LR when improvement stalls. Lightning's `LearningRateMonitor` plus an `lr_scheduler` like ReduceLROnPlateau can automate this.
  * **Recommendation:** If unsure, start with StepLR (Nixtla's default) or a mild cosine decay. For example, initial LR = 1e-3 and after each epoch or every few hundred steps decay it by factor 0.5. The key is to avoid too high an LR after the model has started to overfit noise. Financial data often benefits from a slightly lower base LR and possibly longer training to average out noise.
  * Also consider **gradient clipping** (Lightning by default might clip grad norm=10 if not specified) to avoid exploding gradients given the volatility.

* **Early stopping:** Configure early stopping to prevent overfitting noise. All NeuralForecast models have an `early_stop_patience_steps` parameter (default `-1` = no early stopping). Setting this to a positive integer enables early stopping on the validation loss. For example, `early_stop_patience_steps=3` will stop training if the validation loss hasn't improved after 3 validation checks. The frequency of validation checks is governed by `val_check_steps` (default 100 steps). In practice, you might set `val_check_steps` so that validation happens once per epoch (if an "epoch" is one pass over the training windows) or every N batches. With \~2880 hourly points and windowing, the number of training windows depends on `input_size` and `step_size`. Suppose `input_size=168` and `step_size=1` (NeuralForecast uses `step_size` to potentially skip some steps between training samples), then \~ (2880-168-24) ≈ 2688 training samples are created. Batch size 32 yields \~84 steps per epoch. If `val_check_steps=80`, you'd validate \~once per epoch. Setting patience in terms of steps is a bit unintuitive, but roughly `patience_steps=5` with `val_check_steps=80` means \~5 epochs with no improvement triggers stopping. We recommend using early stopping given the noise: e.g. **patience \~5–10 validation rounds**. This prevents the model from overshooting into overfit territory once the validation metric starts to degrade. It's also good to set a **maximum training steps** (e.g. `max_steps=1000` or `max_epochs` if using epoch-based) as a safeguard. For PatchTST on a few thousand points, you likely won't need extremely long training – a few hundred to a thousand gradient updates might suffice if using a decent batch size and LR. Monitor validation loss or error metrics like MAPE; if they flatten or start rising, training can stop.

**PatchTST quick reference:** For 1H BTC data, a sensible PatchTST configuration might be:

* `h=24` (forecast 24 hours ahead, adjust as needed),
* `input_size=168` (use one week of history),
* `patch_len=24, stride=12` (daily patches, overlapping half a day),
* `hidden_size=128, n_heads=8, n_layers=3` (keep model moderately sized given 8GB GPU),
* `dropout=0.2` (regularize due to noise),
* `revin=True` (de-trend each window) with `revin_affine=False`,
* `scaler_type='robust'` (robust normalization per window),
* `loss=HuberLoss()` or `HuberMQLoss(level=[50,90])` (robust loss, see Section 4),
* `early_stop_patience_steps=5` (stop if 5 val checks with no improvement),
* `learning_rate=1e-3` with `num_lr_decays=3` or use a custom scheduler (cosine annealing),
* `batch_size=32` (32 windows per batch; adjust if OOM).

When initializing the model in code, it would look like:

```python
from neuralforecast.models import PatchTST
from neuralforecast.losses.pytorch import HuberLoss
model = PatchTST(h=24, input_size=168,
                 patch_len=24, stride=12,
                 hidden_size=128, n_heads=8, n_layers=3,
                 dropout=0.2,
                 revin=True, revin_affine=False, revin_subtract_last=False,
                 scaler_type='robust',
                 loss=HuberLoss(), valid_loss=None,
                 early_stop_patience_steps=5, val_check_steps=80,
                 learning_rate=1e-3, num_lr_decays=3)
```

This config is a starting point – you would tune `patch_len/stride` and others (possibly via Optuna – see section 10) to find the best combination for your data. PatchTST is quite powerful for long sequences, but keep an eye on prediction speed: PatchTST's **prediction can be slower than training** if not optimized, due to computing attention for each series/token (users have noted PatchTST prediction being slower than expected). However, for a single series with modest horizon, it should be fine. If prediction latency becomes an issue, you can explore Nixtla's efficient prediction options or consider simpler models for real-time use.
## 3. Alternative Model Architectures (LSTM, GRU, TFT, etc.)

While PatchTST is a top performer, it’s wise to consider other neural architectures, either for comparison or ensembling. Nixtla’s NeuralForecast library offers many model types, including RNN-based models, CNN-based models, and hybrids like the Temporal Fusion Transformer (TFT). Each comes with trade-offs in complexity, interpretability, and speed:

* **LSTM / GRU (Recurrent Neural Networks):** These classic RNNs are suitable for moderate sequence lengths and can capture temporal dependencies through their gated recurrence. NeuralForecast provides `LSTM` and `GRU` models, which by default can output forecasts either *iteratively or directly*. In Nixtla’s implementation, RNN models have a parameter `recursive` (internally, `recursive=False` means the model uses a direct multi-step output layer; `recursive=True` would use the one-step RNN output fed back in for multi-step prediction). By default, LSTM/GRU in NeuralForecast are set to “Both” modes, meaning you can choose direct or recursive forecasting. For example, if you want a GRU that predicts one step at a time (perhaps to mirror a DeepAR style), you could set `recursive=True` and `h=1` for training, then roll it out for H steps. Alternatively, leave `recursive=False` and `h=H` to train it to output an H-step sequence via a final dense layer.

  *Attention & bidirectionality:* Standard LSTMs/GRUs consider past information only. One way to enhance them is adding an **attention mechanism** on top of the RNN outputs. Nixtla does not have a built-in “LSTM with attention” model, but you can approximate this with TFT (which uses gating and attention) or implement a custom model. A classic attention augmentation would be a Bahdanau or Luong attention that weights past hidden states when forecasting each step. Implementing that from scratch would require using the Nixtla `BaseModel` API or training a PyTorch model outside NeuralForecast. Another approach to get similar benefits is using **bidirectional RNNs** (processing the sequence forward and backward). However, note that bidirectional RNNs in forecasting can only be applied *during training on historical data* – at forecast time, you don’t have “future” context for the backward direction. They could be used to better encode past sequences (by utilizing a backward pass up to time *t*), but true bidirectional (with future context) would leak information. So, in practice, you might not use a strictly bidirectional layer for multi-step forecasting, but you could use it in an encoder-decoder setting where the backward pass helps encode the recent history more richly (this is more complex and not directly supported by NeuralForecast out-of-box). Given these complexities, many practitioners prefer transformer-based models for long contexts rather than heavily modifying LSTMs.

  If you prefer simpler RNNs: **GRU** is a bit faster and simpler than LSTM (one less gate), often performing similarly. These models might train faster than PatchTST on small data, and can be more forgiving on very noisy data if configured with fewer parameters. When using LSTM/GRU, consider adding dropout on the RNN layers (`dropout` parameter) to avoid overfitting short-term patterns. Also limit the number of layers or hidden units given the 8GB memory (e.g. an LSTM with 2 layers × 64 hidden units is lightweight). You could also try a **dilated RNN**.

* **Dilated RNN:** Nixtla includes a `DilatedRNN` model which uses dilated recurrence (skipping certain time steps in connections). Dilated RNNs are designed to capture long-term patterns without having to backprop through every time step (they effectively create shortcuts in the RNN state update). This can help when long lags (like seasonality or long memory) are important. For example, a dilated RNN might connect the state at time *t* to time *t-24, t-168*, etc., enabling it to learn daily/weekly effects more directly. Such architecture might suit BTC if weekly cycles are significant and you want a simpler model than a full transformer. Configuration for `DilatedRNN` would involve specifying dilation factors or it may use a preset scheme internally. Compared to PatchTST, a dilated RNN will be faster per step and use less memory, but might not capture very complex patterns as well. It’s a good middle ground if you find transformers too slow.

* **Temporal Fusion Transformer (TFT):** TFT is a sophisticated model that combines LSTM encoders with multi-head attention and gating mechanisms for *interpretability*. Nixtla’s `TFT` model implements the architecture from Lim et al. (2019). **Why use TFT?** It excels when you have multiple *exogenous features* and need to understand their impact. TFT has components like:

  * A **Static Covariate Encoder** (to process static features like asset class, etc.),
  * A **Historical LSTM Encoder** for past values,
  * Attention mechanisms (variable selection networks, and an interpretable multi-head attention for forecasting) that provide weights indicating each feature’s importance at each time.
  * It produces not only forecasts but also attention weights that can be used to explain which features or time steps were most influential in the prediction.

  For Bitcoin, if you incorporate features such as trading volume, technical indicators, day-of-week flags, etc., TFT can help identify which of these were relevant for a given forecast. For instance, TFT might learn that on weekends (captured by a weekend flag exogenous variable) the model relies more on a “volatility” feature and less on trend features, etc. The attention outputs can be parsed to quantify such effects. **Configuration:** You’ll specify lists of exogenous features for TFT (it supports `futr_exog_list`, `hist_exog_list`, `stat_exog_list` similarly to other models). You also choose hidden layer sizes for the TFT’s internal networks (there are several: one for variable selection, one for the main LSTM, etc.). Nixtla likely provides sensible defaults, but be aware TFT is parameter-heavy. It may require more memory – if using an 8GB GPU, you might need to reduce batch size or hidden dimensions for TFT. It’s also a bit slower to train due to its complex forward pass.

  The payoff is **interpretability**: after training, you can extract the importance of each feature (static or time-varying). Nixtla’s TFT implementation likely stores the attention weights or has methods to get them. Even if not directly exposed, you could manually retrieve them via the model’s `attention_weights` attribute or by modifying the code. We cover feature importance more in Section 5, but note that TFT was specifically designed for that purpose – giving it an edge if explainability is crucial.

* **CNN/TCN models:** Convolutional approaches like Temporal Convolutional Networks (TCN) or other CNNs (Nixtla has `TCN`, `BiTCN`, etc. in its model zoo) can also be applied. These use 1D convolutions with dilations to capture long-range patterns (TCN is known for very long receptive fields with relatively few parameters). They are typically faster to train than transformers (convolutions are efficient) and can handle long sequences by design of dilation. If your GPU memory is limited, a TCN might be a good alternative to PatchTST: it will not blow up in memory usage as sequence length grows, since convolution filters slide over the sequence. Nixtla’s `TCN` likely has parameters like number of layers, kernel size, dilation growth, etc. For BTC, a TCN could capture periodic patterns (via the dilated filters) and sudden changes (via its use of recent local filters). It might not match a transformer’s accuracy on complex signals, but it’s worth testing. A variant, **N-BEATS and N-HiTS** (which are MLP-based but with clever hierarchical decomposition), are also in Nixtla. They often perform well on clean seasonal data, though for noisy financial data their advantage is less clear. However, Nixtla’s *NHITS* is a strong model that can decompose time series into multi-scale components and might capture different frequencies (the official NHITS is known to do well and is relatively lightweight).

* **Hybrid and advanced models:** Nixtla includes cutting-edge models like `Autoformer`, `Informer`, `FEDformer`, and even experimental ones like `TimeMixers`, `TimeGPT-LLM` etc.. These are various transformer architectures optimized for time series (Informer uses probabilistic sampling to sparsify attention, Autoformer uses seasonal-trend decomposition within the model, etc.). For completeness:

  * *Informer/FEDformer*: Good for very long horizons with some periodicity; might be overkill for 24-168 horizon but could try for multi-week forecasts.
  * *N-BEATSx/N-HiTS*: Allow incorporating exogenous variables into N-BEATS-like frameworks; can model seasonality explicitly. Possibly useful if BTC had known seasonal cycles (not obvious beyond weekly).
  * *KAN, RMoK*: These are advanced mixture models (Kalman neural networks, etc.), probably not needed here unless exploring probabilistic aspects deeply.
  * *TimesNet*: A newer model using time-embedding blocks; potentially good but not as battle-tested.
  * *DeepAR*: Nixtla’s `DeepAR` (RNN with probabilistic output) is available. This is worth noting: **DeepAR (Amazon’s)** uses an RNN (LSTM) to model the distribution of the next point given past, typically assuming a parametric distribution (like Student-t). If you prefer a probabilistic approach without quantiles, DeepAR is an option. It outputs parameters of a distribution at each time step and can sample future paths. It’s inherently an *iterative forecasting* model (one step at a time). For BTC’s volatile nature, you might choose a Student-t likelihood for heavy tails. Nixtla’s DeepAR likely supports specifying the likelihood (`distribution` argument). The benefit is you get uncertainty estimation directly and can do **Monte Carlo sampling** to produce prediction intervals or probabilities of large moves. The downside is you need to ensure the distribution assumption fits (Student-t is usually fine for returns). In practice, Huber/quantile losses plus ensembling might be simpler than tuning a DeepAR’s likelihood, but it’s an option for advanced users.

* **Crypto-specific hyperparameter guidance:** Tuning models on crypto data often requires stronger regularization and careful hyperparam choices due to high noise:

  * Use **higher dropout** rates or weight decay than you would on, say, a stable load forecasting problem. Overfitting to short-term noise is a big risk. For example, set dropout 0.2–0.3 in RNNs/transformers, and consider L2 weight decay on weights to prevent overly complex fits.
  * Consider a **smaller hidden size** relative to data length. If your series is only \~3000 points, a huge network can memorize it. For LSTM/GRU, hidden sizes in the tens (32, 64) might suffice. For transformers, maybe 128 or 256 hidden dimensions (with multi-head attention) are enough; larger might just overfit or run out of memory.
  * **Shorter training epochs with early stopping** – crypto can have structural breaks (e.g., a regime change where relations between features and target shift). An aggressively trained model might latch onto patterns that don’t hold in the future. By stopping early or using a modest number of epochs, you essentially do *implicit regularization*, letting the model capture the big picture patterns but not every wiggle.
  * **Hyperparameters for volatility:** If your model has a concept of output distribution (like DeepAR’s scale parameter or if you use quantile loss), allow it to be flexible. For instance, in DeepAR, you might not want to fix the scale per series; let the model learn time-dependent scale if possible.
  * **Sequence length vs. recency:** Sometimes, *less is more* for volatile series. Using a very long input window can introduce a lot of older, irrelevant data (e.g., year-old patterns may not help predict next week if the market regime is different). There is a case for using a **rolling training window** (only last N days of data) to train or fine-tune your model so that it emphasizes recent patterns. This isn’t a hyperparameter of the model per se, but part of data handling – an expanding window vs rolling window approach (we discuss in Section 4, Walk-forward validation). If you find that including all 120 days in training hurts because early data had a different volatility regime, you might limit training to the last 60 days, for example.
  * **Learning rate** might need to be lower if the data is very noisy, to avoid the optimizer getting trapped by noise. Alternatively, a high initial LR with rapid cooldown can act like simulated annealing to avoid local minima. It’s something to tune (Optuna can help find ideal LR, dropout, hidden size, etc.).
  * **Ensemble multiple initializations:** One hyperparam often overlooked is random seed. Different random weight initializations might yield significantly different forecasts in noisy data. An effective strategy for stability is to train multiple instances of the same model with different seeds and average their predictions (this reduces variance). It’s not a hyperparameter in the usual sense, but a way to get a more robust predictor (effectively a model ensemble, see Section 7).

In summary, Nixtla’s framework gives you a toolkit of models. It’s prudent to try a few: e.g., a **GRU (with attention)** for a lightweight approach, a **PatchTST** for state-of-art accuracy, and a **TFT** for interpretability. You may find that simpler models like a two-layer LSTM or even a feed-forward network (MLP) with lags can perform reasonably for short horizons – especially when combined with statistical models. Use crypto domain knowledge to guide: for instance, if you suspect that *“momentum”* and *“mean-reversion”* regimes alternate, an RNN might adapt quicker (due to its state) whereas a big transformer might average them out. Conversely, if there are subtle calendar effects or interactions of features, TFT might uncover them. Keep these models in your toolbox to either switch or combine (ensemble) as needed.

## 4. Losses & Metrics for Volatile Time Series

Choosing the right loss function and evaluation metrics is crucial for a volatile series like BTC, where large outliers and directional accuracy matter. Here’s how to handle it:

* **Huber Loss (Smooth L1):** A highly recommended loss for crypto price forecasting is the Huber loss, which is less sensitive to outliers than MSE. The Huber loss is quadratic for small errors (like MSE) but becomes linear for large errors. This means if the model makes a huge mistake on an unexpected price spike, the error contribution is linear rather than squared – preventing a single outlier from blowing up the loss and dominating training. Nixtla provides Huber loss implementations. For point forecasting, you can use `HuberLoss()` as the `loss` in model config (as we did for PatchTST above). For probabilistic forecasting (predicting quantiles), Nixtla has `HuberMQLoss` (Huber for multiple quantiles). For example, in their robust forecasting tutorial they compare an NHITS model with Huber quantile loss vs normal quantile loss and show Huber drastically reduces error on outliers. We recommend using Huber loss or a variant for training BTC models.

  If you plan to forecast only a point estimate (say the median), Huber is a good compromise between MSE and MAE. If you plan to produce full prediction intervals, you might use a quantile loss (pinball loss) to directly optimize quantile forecasts. In that case, consider a **tilted Huber**: one approach is optimize quantile loss for central quantiles but use Huber for the median or as a regularizer. However, Nixtla’s `MQLoss` is already quite robust if you set multiple levels.

* **Custom directional loss:** Often in trading, the direction of the move is as important as the magnitude. You may want a loss that penalizes getting the sign wrong more than the magnitude error. While Nixtla doesn’t have a built-in “directional accuracy” loss, you can incorporate this concept:

  * **Metric monitoring:** Track *directional accuracy (DA)* as a metric: DA = percentage of times the forecast correctly predicts up/down movement. If \$y\_t\$ is the actual and \$\hat{y}*t\$ the forecast, for horizon \$h\$ you could define DA@\$h\$ = mean\$(\text{sign}(\hat{y}*{t+h} - y\_{t}) = \text{sign}(y\_{t+h} - y\_{t}))\$. In other words, did you predict the correct direction from time \$t\$ to \$t+h\$. This can be computed after forecasting, on validation or test sets.
  * **Loss shaping:** If you want to train the model to favor correct direction, you could add a term to the loss. For instance, a simple approach: multiply the loss by a factor >1 if the sign is wrong. E.g., `loss_t = base_loss * (1 + gamma * I(sign wrong))`. This would make errors where the direction was wrong effectively gamma\*100% larger. Tuning gamma controls how much you prioritize direction vs magnitude. Keep gamma small to not destabilize training (maybe 0.1 or 0.2). Another approach: use a differentiable approximation of sign correctness – e.g., a soft version: \$\text{tanh}(\Delta y \* \Delta \hat{y})\$ tends to +1 if signs agree, -1 if not, you could incorporate something like \$-\lambda \tanh((y\_{t+h}-y\_t)(\hat{y}\_{t+h}-y\_t))\$ into the loss to encourage positive values (correct sign). However, this gets tricky and may not yield huge gains in practice.
  * **Alternate strategy:** Instead of modifying loss, you can train a secondary classifier model to predict up/down and use its output to inform forecasts (beyond our scope, but ensemble classification + regression).

  In summary, for simplicity, use Huber or MAE-based losses for robust magnitude prediction, and just monitor directional accuracy as an **evaluation metric**. You can report metrics like *DA\@1-hour, DA\@24-hour* etc. and ensure they meet your needs. If the model has poor directional accuracy, consider adjusting training (maybe emphasize recent data or incorporate a sign penalty as above).

* **Multi-horizon evaluation:** When forecasting multiple steps ahead (e.g. a full 24-hour trajectory), evaluate performance at each horizon as well as overall:

  * Compute common metrics like MAE, MAPE, RMSE for each forecast horizon \$k\$ (1-hour ahead, 2-hour ahead, ..., 24-hour ahead). Often, error grows with horizon, and this tells you how degradation happens. For example, you might find 1-3 hour forecasts are quite accurate but 20+ hours ahead are nearly baseline – which could direct you to focus on improving long horizon or limit how far you forecast confidently.
  * Also compute a **summary metric** over all horizons. A typical one is the sMAPE or MAPE averaged across all forecast points. Nixtla’s `utilsforecast.evaluation.evaluate` function can aggregate metrics across forecast windows. For instance, if you do a backtest and get a dataframe of predictions vs actuals for all horizons, you can call `evaluate(df, metrics=[mape, smape], agg_fn="mean")` to get the average MAPE over all forecast horizons.
  * Another advanced metric is **MASE** (Mean Absolute Scaled Error), which scales errors relative to a naive baseline. It’s useful to compare models across different data scales. If you do backtesting, computing MASE for each horizon (with seasonal naive as baseline, e.g. last week same hour as baseline) might be insightful.
  * For probabilistic forecasts, use metrics like **Pinball loss** for quantiles or **CRPS (Continuous Ranked Probability Score)** for full distribution. If you output quantiles (e.g. 10th, 50th, 90th percentiles), average quantile loss is essentially CRPS. Nixtla’s `MQLoss` gives you that on validation if using quantile training. You can also evaluate calibration: e.g., check what percent of actuals fall below the predicted 90% quantile – ideally \~90%. These give a sense of uncertainty estimation quality.
  * **Walk-forward/backtesting:** We highly encourage performing a walk-forward validation to evaluate multi-horizon performance (see below). Nixtla’s `NeuralForecast.cross_validation` method simplifies this by automatically training and forecasting on multiple rolling windows. For example, `nf.cross_validation(Y_df, n_windows=4, step_size=24, refit=True)` could create 4 splits and compute forecasts. The result is a dataframe with actuals and predictions for each window. From that, you can compute metrics per window or overall. If you set `refit=True`, the model is refit at each window (simulating retraining periodically), which typically yields better multi-horizon error but is realistic for production if you plan to retrain regularly. If `refit=False`, it uses one model to predict all windows (like simulating one training and multiple forecasts) – this tests stability of one model over time.

* **Walk-forward vs Expanding Window Validation:** These are strategies for time series model validation:

  * *Walk-forward (rolling origin) with refit:* Train on data up to time \$T\_0\$, forecast next \$h\$ steps; then extend training data to \$T\_0+h\$ (or \$T\_0+\$ some step) and refit model, forecast next \$h\$, etc. This mimics a procedure where you retrain the model regularly as new data arrives. It’s computationally heavier (multiple fits) but yields the best use of data at each forecast point. NeuralForecast’s `cross_validation` can automate this by specifying `val_size` and `test_size` or `n_windows` with `refit=True`.
  * *Expanding window (growing training set) vs Sliding window:* If you do `refit=True` and always start from the beginning, you’re doing expanding window (the model sees all data up to the forecast point). Sometimes, you may prefer a sliding window (only the most recent N days of data for training each time, discarding old data). Nixtla’s utility doesn’t directly discard old data when refitting (it will use the full history unless you limit it manually). If you want a sliding window in cross\_val, you’d have to subset `Y_df` inside a loop manually. For instance, for each cutoff date, filter the training data to only last 60 days before cutoff before fitting. This approach can handle non-stationarity by not training on very old regimes.
  * *No-refit (single training) expanding window forecast:* Another scenario is train once on earliest period, then *without retraining* produce forecasts at multiple future points by feeding the model new actuals as they become available (for models that support updating state). Some RNNs can be used this way (maintaining hidden state). However, with Nixtla’s interface, typically you train once, then forecast \$h\$ steps, then you could update your DataFrame with actuals and call predict again for next \$h\$. That approach assumes the model can incorporate new true data without weight updates, which works if the model is global and can accept more input sequence. In practice, results degrade if you don’t refit at all in a long horizon scenario – especially if distribution shifts.
  * **Recommendation:** Use *walk-forward with refit* for evaluation. It simulates a realistic pipeline where you would retrain (or at least update) the model frequently for a volatile asset. This will give you an estimate of performance in production if you plan to retrain, say, daily or weekly. For example, do a backtest on the last 30 days: train on older data, forecast 1 day, advance 1 day, retrain including that day, forecast next day, etc. This yields 30 1-day forecasts all from models that had only past info at the time. Compute metrics on those. This tends to show better accuracy than a single model used for 30 days without update, which is expected in crypto (since you *will* update your model in practice).

To implement cross-validation with Nixtla:

```python
from neuralforecast import NeuralForecast
from neuralforecast.models import PatchTST, LSTM
# ... define models list ...
nf = NeuralForecast(models=[PatchTST(...), LSTM(...)], freq='H')
nf.fit(train_df)  # fit on initial training set
# Rolling origin evaluation: 3 folds, predicting 24h each, refit model each time
cv_df = nf.cross_validation(train_df, n_windows=3, step_size=24, refit=True)
print(cv_df.head())
```

This `cv_df` will contain columns `unique_id`, `ds`, `y` (actual), and forecast columns for each model. It also adds a column `cutoff` to indicate the training data cutoff for each window. You can then calculate metrics by grouping by `cutoff` or overall. For example, to compute overall MAPE for PatchTST in the CV:

```python
from utilsforecast.evaluation import mape, evaluate
df_metrics = evaluate(cv_df, metrics=[mape], models=['PatchTST'], agg_fn='mean')
print(df_metrics)
```

This would give you average MAPE across all windows for PatchTST. You could also compute per-window metrics to see variation.

In evaluating, also consider the **naive benchmarks**: e.g., a *naive forecast* that “tomorrow’s price = today’s price” or *seasonal naive* that “price at hour t+24 = price at hour t” (24h lag). These are easy to compute and provide a baseline. Often with MASE, you use the naive forecast error as denominator. It’s good to ensure your model beats a naive forecast (which in crypto might be a high bar if markets are close to random walk).

In summary, use robust losses like Huber for training, track both magnitude and directional metrics, and validate thoroughly with walk-forward tests. This will ensure your model is not just fitting historical noise but truly has predictive power on unseen data.

## 5. Feature Engineering and External Regressors

Feature engineering can significantly enhance a forecast if the added features carry predictive signal. For Bitcoin, you can integrate **technical indicators**, calendar features, and external data (when available). Here’s how to do it in a pipeline with NeuralForecast (and MLForecast):

* **Technical Indicators (`ta` library):** Indicators such as RSI (Relative Strength Index), MACD (Moving Average Convergence Divergence), Bollinger Bands, etc., are widely used in crypto trading. These indicators summarize recent price/volume patterns that might foreshadow short-term movements. For example, RSI provides a bounded momentum oscillator, and extreme RSI values could precede reversals. You can compute these using the `ta` library (`pip install ta`). Usually, you’ll add them as new columns in your DataFrame. **Important:** Most technical indicators are calculated from past prices (and possibly past volume), so they are **historical exogenous variables** in forecasting terms – meaning their values up to time \$t\$ are known at time \$t\$, but their future values beyond \$t\$ are *not known* (because they depend on future price). This means you include them as features for the model’s input windows, but you **do not have future values for them** when forecasting. The model must operate without them in the future, or rather, it can’t directly use “future RSI” because that’s essentially the target itself transformed.

  How to include such features in NeuralForecast:

  1. Compute the indicators for your historical data. E.g., using `ta`:

     ```python
     import ta
     df['rsi14'] = ta.momentum.RSIIndicator(close=df['y'], window=14).rsi()
     df['ema20'] = ta.trend.EMAIndicator(close=df['y'], window=20).ema_indicator()
     df['volatility_atr'] = ta.volatility.AverageTrueRange(high=df['High'],
                                                          low=df['Low'],
                                                          close=df['y'], window=14).atr()
     # (if you have high/low price separate; if not, ATR might not apply)
     ```

     This adds columns for 14-period RSI, 20-period exponential moving average, and Average True Range as examples. You can add many indicators, but be cautious: too many highly correlated features can add noise and slow training. Focus on a few that represent different aspects (momentum, trend, volatility).
  2. Decide which are **historical exogenous** (`hist_exog_list`) and which are **future exogenous** (`futr_exog_list`). Technical indicators based on price are known only up to the latest observed time, not for future timestamps (since future price is unknown). So RSI, moving averages, etc. are *historical exog*. On the other hand, **calendar features** (like day-of-week, hour-of-day, holiday flags) are **future-known** – we know the day and hour for future timestamps. Those would go in `futr_exog_list`.
  3. Provide these feature names when initializing the model. For example:

     ```python
     model = PatchTST(..., hist_exog_list=['rsi14','ema20'], futr_exog_list=['hour','is_weekend'])
     ```

     In NeuralForecast, you’ll also need to include the feature columns in the DataFrame that you pass to `fit()` and `predict()`. The DataFrame for training should contain `unique_id, ds, y, rsi14, ema20, hour, is_weekend, ...` (with values for each timestamp). The `hist_exog_list` and `futr_exog_list` just tell the model which columns to actually use.

     Under the hood, NeuralForecast will ensure that during training, the network gets input vectors including these exogenous features aligned with the target sequence. For `hist_exog_list` features, it will take their values over the input window; for `futr_exog_list`, it can incorporate known future values for the forecast horizon (for instance, the model can know that the next 24 hours include a weekend or not).

     Note: If using Nixtla’s `MLForecast` for machine-learning models, the process is slightly different. MLForecast has its own way of adding features (like lag features or transformations). For MLForecast, you might directly specify `lag_transforms` for some indicators or just compute them and supply to the model as static features (depending on use). But for NeuralForecast (deep models), the above approach stands.

  Example – adding a **volume** feature: If you have Bitcoin trading volume data aligned with price, volume can be a powerful predictor of volatility or trend strength. Volume is known for past times, and you typically don’t know future volume (unless making assumptions), so it’s a `hist_exog`. You’d add `volume` column to the DataFrame and include `'volume'` in `hist_exog_list`. The model might learn that an upcoming large move is often preceded by unusual volume patterns in the hours before.

  **Scaling features:** All features should be scaled appropriately to avoid dominating the network inputs. If you use NeuralForecast’s internal scaling (like `scaler_type='robust'` or `identity`), note that by default it scales the *target window*. It’s a bit unclear from docs if exogenous features are scaled automatically; likely, **historical exog are scaled similarly to the target** (especially if using window normalization, they might concatenate features and scale together, or scale each feature independently – Nixtla docs on exogenous don’t explicitly say). To be safe, you might manually scale some features:

  * For RSI (range 0–100), you could normalize to 0–1 by dividing by 100, or just leave as is since 0–100 is not a huge range.
  * For volume, it might span orders of magnitude, better to take log(volume) or at least scale it to a comparable range. Many traders use log volume or volume change rate rather than raw volume.
  * For price itself, if you didn’t log-transform, robust scaling/RevIN will handle it in windows. But external regressors not tied to price scale might need individual scaling.

* **Calendar & seasonal features:** Bitcoin trades continuously, but there are still temporal patterns (e.g., maybe less activity on weekends, or intraday cycles corresponding to major market hours when institutional investors trade). Adding **day-of-week** and **hour-of-day** features can help the model learn such periodic effects. These are **future-known** (we always know what day/hour the future timestamps will be). Implementation:

  * Create features like `day_of_week = ds.dt.weekday` (0=Monday,...6=Sunday), `hour = ds.dt.hour`.
  * These can be encoded as one-hot vectors or continuous cyclical features (sine/cosine). A typical approach is to encode cyclical features via sine and cosine transforms: e.g. `sin_hour = sin(2*pi*hour/24)`, `cos_hour = cos(2*pi*hour/24)`. Similarly for day\_of\_week (7-day cycle).
  * Alternatively, if using tree models in MLForecast, leaving them as categorical is fine. For neural nets, one-hot or sin/cos are better to not confuse ordinal meaning (Tuesday is not “greater” than Monday, etc.). Since Nixtla deep models likely accept numeric features, using sin/cos is a good idea.
  * Include these in `futr_exog_list=['sin_hour','cos_hour','is_weekend', ...]`. You will provide their values for the entire timeline and also need to provide future values for the horizon when forecasting. NeuralForecast’s `predict()` method can accept a future exogenous DataFrame (`X_df`) for the forecast periods. So you would need to construct a small DataFrame of future timestamps with those features to generate the forecast.
  * For example, to forecast next 24h, create a DataFrame `future_df` with 24 rows: `unique_id='BTC'`, `ds` from last known timestamp+1h to +24h, and columns for `sin_hour`, `cos_hour`, etc. Then call `nf.predict(futr_df=future_df)` to get forecasts.
  * Calendar features ensure the model can account for periodic effects easily rather than forcing the neural net to learn sine-wave patterns from raw data (which it can, but giving it explicitly is more data-efficient).

* **Static features (if any):** Static exogenous (`stat_exog_list`) would be attributes of the time series that do not change over time. For example, if you were modeling many assets, a static feature could be market capitalization or sector of each asset. For a single series, static features are not very relevant (you could include something like “asset\_type=crypto” if your model were global across assets of different types). Likely skip for BTC-only scenario.

* **Feature importance via attention/gradients:** Once you’ve trained a model with these features, how do you know which features matter? There are a few ways:

  * **TFT attention:** As mentioned, TFT provides directly interpretable weights. If you train a TFT model, you can inspect its internal variable selection weights. For example, TFT has a “variable selection network” that produces a weight for each input feature at each time step (for past inputs) and a gating mechanism for each static feature. Nixtla’s `TFT` documentation shows that all models (including TFT) accept exogenous variables similarly. After training, one approach is to use the `predict_insample` function to get the model’s internal contributions. Nixtla also has an **“Interpretable Decompositions”** tutorial for N-BEATSx/N-HITS that shows how those models decompose forecasts into trend, seasonality components. For TFT specifically, you might need to dig a bit: possibly by retrieving `model.attention_weights` for the decoder self-attention or variable selection gate outputs. This may require reading Nixtla’s code or forums for guidance.
  * **Attention in PatchTST/Transformers:** PatchTST itself is a transformer, and it can optionally store attention weights (I see a parameter `store_attn=False` in PatchTST; if set True, it might keep the last attention matrix). You could analyze which time positions were attended to. However, PatchTST’s *channel independence* means each univariate channel’s attention is separate. You could see, for example, that the model attending strongly to the immediate past vs a week ago. But interpreting transformer attention in forecasting is less straightforward for feature importance compared to TFT's designed interpretability.
  * **Permutation importance / Ablation:** A model-agnostic way is to measure performance when a feature is randomized or omitted. For instance, after training, you can try: set the RSI feature values to median (or shuffle them among time steps) and see how much the forecast error increases. If error jumps notably, RSI was important. Or retrain models leaving one feature out each time (leave-one-feature-out analysis) to see impact on validation loss. This can be expensive if many features.
  * **Gradients and SHAP:** You can treat the trained neural network as a function and use gradient-based attribution. For example, use **Integrated Gradients** or **Saliency maps** for time series: for a given forecast output, compute the gradient of that output w\.rt each feature input at each time. This tells you how sensitive the prediction is to that feature at that time. If you average absolute gradients over many samples, you get a sense of which features/timepoints influence the model most. Libraries like Captum (for PyTorch) can help apply integrated gradients. You’d feed in an input (a window of data with all features) and get attributions.
  * **SHAP values:** SHAP (Shapley Additive Explanations) can be applied to time series models as well, though it’s heavy for deep nets. You could use the DeepExplainer variant for PyTorch to get feature attributions. SHAP will tell, for each prediction, how each feature’s value contributed to pushing the forecast up or down relative to some baseline. For example, SHAP might reveal that *“because the 10-hour momentum was very high, the model increased the forecast by \$X\$”*. Doing SHAP for every time step in a window is complex, but you could aggregate by feature.
  * **Using MLForecast for feature importance:** If you also build an XGBoost/RandomForest model via MLForecast with the same features, tree-based models give feature importance “for free” (e.g. split importance). You can inspect XGBoost’s feature importance as a quick check of which features it found predictive. Often, a deep model might implicitly find similar importance, so it’s a sanity check.
  * **Example:** Suppose you included `volume` and `RSI`. You might find that whenever `RSI > 70` (overbought) the model tends to predict a downward correction – you could verify this by partial dependence: fix RSI high and see forecast output. Similarly, see how forecast changes with volume spikes. This kind of analysis can be done by feeding hypothetical inputs to the model.

* **Adding multiple external regressors:** You are not limited to technical indicators. Other potential exogenous inputs for BTC:

  * **On-chain metrics:** e.g. hash rate, active addresses, transaction volumes – if you have those daily or hourly, they could be useful. Many on-chain metrics are daily, which might be too low-frequency for an hourly model, but you could include the daily value as a step function over the hours, or an interpolated value. These would be known for past and (for today maybe but not for future unless you assume persistence).
  * **Macro indicators:** If you believe BTC is influenced by, say, stock market indices or interest rates, you could include something like S\&P 500 futures price as a feature (BTC often had some correlation with equities). For example, include a scaled version of S\&P or Nasdaq index (which is available during trading hours; on weekends maybe hold last value or treat differently). This is external market data – known up to current time but not known for future (unless you separately forecast it or assume some scenario). If you include it as a historic exog, the model sees how BTC moves with S\&P historically. For forecasting, you’d either need a predicted path for S\&P or just leave that feature blank – realistically, you might not want to rely on predicting another asset. Another way: use *contemporaneous* features – e.g. if you have BTC and ETH prices, maybe use ETH’s current price or recent return as a feature for BTC’s next move (since sometimes one leads the other). That again requires having ETH forecast or using it as a known lead indicator if any.
  * **Regime or sentiment indicators:** Perhaps use a volatility index (if one exists for crypto, like the BitVol index) or funding rates from futures markets, or social media sentiment scores. These can be included similarly if you have the data historically. Future values of these might not be known, but if they’re mean-reverting, you could assume they stay near current level for the forecast or use an average.

  The key is to ensure any feature you add either has a known future trajectory (futr\_exog) or you accept that the model must work without future knowledge of it. NeuralForecast will handle this by, during prediction, requiring you to provide future exog values for any features in `futr_exog_list`. For hist\_exog features, you don’t provide future values; the model will rely on them in the input window and then forecast without needing an explicit future exog for them. So if a feature is very important but only historic, the model’s forecast could effectively extrapolate that influence in the short-term but can’t account for changes in that feature beyond the input window. For instance, if high volume usually precedes a jump, the model will see the high volume in the recent input and can predict a jump. But if volume suddenly were to drop in the future, the model wouldn’t know because it had to forecast volume-less. In practice, that’s just a limitation we accept unless we forecast volume too.

**Summary of best practices for features:**

* Leverage **technical indicators** to provide the model with synthesized signals of momentum or mean reversion (include as `hist_exog_list`). Scale or normalize them sensibly.
* Add **calendar features** (hour, weekday) as `futr_exog_list` so the model doesn’t need to relearn calendar patterns from scratch.
* If available, incorporate relevant **exogenous data** like volume, social sentiment, or related asset prices (likely as historic exog).
* Use Nixtla’s consistent long format for these features: each exogenous variable is a column in the DataFrame. NeuralForecast will align them by timestamp. Make sure there are no missing values; if an indicator can’t be calculated for the first few points, fill them or start the series later. For example, RSI(14) can’t be computed for the first 13 points – you might fill those with neutral value 50 or forward fill after 14th point.
* Monitor the **effect of features**. If a feature isn’t helpful, you may drop it to reduce complexity. If a feature is very helpful, consider if the model could use it even more (maybe through an interaction or a better encoding).
* For interpretability, **TFT** or post-hoc analysis can tell you which features are driving the model’s predictions, which is valuable in a financial context to build trust in the model.

By engineering thoughtful features and using NeuralForecast’s ability to handle exogenous inputs, you can potentially improve accuracy and adapt the model to the unique facets of crypto markets. Just be careful to avoid data leakage: only use information that would have been known at the time of prediction (e.g. don’t use tomorrow’s scheduled Fed announcement as a feature unless you truly know it in advance, and if you do, it’s fair game as a futr\_exog since the event calendar is known). With the right features, the model might learn, for instance, *“if it’s a weekend night with low volume, volatility tends to be low – so don’t overreact to a single spike”*, or *“if RSI is very low (oversold) and it's Monday morning in Asia, expect a bounce”* – patterns that pure price-only models might miss.

## 6. GPU Optimization Techniques

Training deep models on GPU can be resource-intensive. With an 8GB GPU (common in, say, a GTX 1070 or some cloud instances), you need to optimize memory usage and training throughput. As your resources increase (16GB, 24GB), you can scale up batch sizes or model complexity. Here are strategies to get the most out of your GPU:

* **Batch Size vs. Memory:** Batch size has a direct linear effect on GPU memory usage (for model activations). For an 8GB GPU, a safe starting batch might be 32 (as Nixtla defaults) for moderate model sizes. If you find GPU memory is not fully utilized (you can check with `nvidia-smi`), you can increase batch size to use more of the GPU and speed up training (to a point of diminishing returns due to I/O or batch normalization issues). Conversely, if you hit *CUDA OOM (Out Of Memory)* errors, reduce batch size first.

  As a rough guideline:

  * **8GB GPU:** For PatchTST with input length \~168 and hidden dim \~128, batch size \~32 should fit. If using a heavier model like TFT or a larger patch embedding (hidden 256+), you might need batch 16 or lower. You might see \~6-7 GB usage at batch 32 for a medium model.
  * **16GB GPU:** Likely can handle batch 64 or more, or allow larger models (hidden sizes 256+, more layers) at batch 32.
  * **24GB GPU:** Gives freedom to use batch into the hundreds (if dataset is large enough) or very large models (like experimenting with long-horizon transformers or multivariate models with many channels).

  Keep in mind that increasing batch size yields diminishing returns on convergence – beyond a certain point, you may need to increase learning rate or adjust epochs to get the same convergence. A common practice is to scale learning rate with batch size (linear scaling rule: if you double batch size, consider doubling LR) to maintain training speed, but monitor stability.

  NeuralForecast also provides `windows_batch_size` (e.g. default 1024) – this suggests they might further batch the creation of windows. If memory is an issue during data loading or forecasting, you can tweak `windows_batch_size` to control how many windows are processed at once when building the training matrix. Similarly, `inference_windows_batch_size` exists; if you encounter memory spikes during `.predict()`, you could set that to a lower number to do inference in chunks (possibly relevant for PatchTST which might otherwise try to predict in one go).

* **Mixed Precision Training:** Utilizing FP16 (half precision) can **halve memory usage** for model weights and activations (approximately) and also **speed up** training on GPUs with Tensor Cores (NVIDIA RTX20 series and newer). PyTorch Lightning makes this easy: you can set `precision=16` in the Trainer or via `trainer_kwargs` when instantiating NeuralForecast (since NeuralForecast likely internally uses a PyTorch Lightning Trainer). For example:

  ```python
  nf = NeuralForecast(models=[model], freq='H', trainer_kwargs={'precision':16, 'devices':1})
  ```

  This will enable automatic mixed precision (AMP) training – meaning weights are float16 where safe, and float32 where needed for stability. Lightning’s AMP uses dynamic loss scaling to avoid underflow. In practice, mixed precision can reduce memory usage by \~50% and often gives a 1.5-2x speed boost on modern GPUs, *without* sacrificing much accuracy. We strongly recommend it on 8GB GPUs to squeeze in larger batches or more complex models. Monitor training; if you see instability (NaNs in loss), Lightning will adjust loss scaling, but if it persists, you might have to disable AMP for that model (rare for most forecasting models).

* **Gradient Accumulation:** If you need a large effective batch size but cannot fit it in memory at once, use gradient accumulation. For instance, if you want batch 128 but only 32 fits in memory, set `accumulate_grad_batches=4` in the Trainer. This way, the model will perform 4 forward/backward passes of batch 32, accumulating gradients, before doing one optimizer step – effectively simulating batch 128 while using memory for 32 at a time. This increases iteration time (since 4x more forwards per step) but can improve convergence if larger batch is beneficial. Lightning `trainer_kwargs` can include `accumulate_grad_batches: 4`. This is particularly useful on 8GB GPUs if you find small batches causing very noisy gradients; instead of raising batch size (which you can't), accumulate.

* **Multi-GPU and Distributed Training:** If you have multiple GPUs (say 2×8GB), you can leverage data parallelism. Nixtla’s `DistributedConfig` or simply Lightning’s `devices` and `strategy` can be used. For example:

  ```python
  nf = NeuralForecast(models=[model], freq='H', trainer_kwargs={
      'accelerator': 'gpu', 'devices': 2, 'strategy': 'ddp', 'precision':16
  })
  ```

  This would use 2 GPUs in Distributed Data Parallel (DDP) mode. The dataset is split and each GPU trains on a subset of the batch. You should increase batch size when using multiple GPUs so each still gets a decent chunk (e.g., if batch 32 on 1 GPU, try batch 64 on 2 GPUs, which gives 32 per GPU effectively). Multi-GPU can nearly halve training wall time if overhead is small and your model is heavy enough to benefit. Nixtla’s distributed tutorial shows how to set up `DistributedConfig` for cluster environments – for local, Lightning's DDP should suffice.

  Also, if you have a single machine with, say, one 24GB GPU, you might not need multi-GPU, but if you had two 12GB GPUs, using both could let you train a model that effectively sees a larger batch or more data in same time. Multi-GPU doesn’t help inference speed for a single series (inference is so fast on one series anyway), it’s purely for training speed or capacity.

* **Profiler and Memory Checks:** To optimize, profile where time and memory go. Lightning has a profiler option or you can use PyTorch’s `torch.profiler`. For memory, the PyTorch function `torch.cuda.max_memory_allocated()` can tell you peak memory use. For example, you can wrap a training step or prediction in code to record memory:

  ```python
  torch.cuda.reset_peak_memory_stats()
  # run training epoch or a forward pass
  peak_mem = torch.cuda.max_memory_allocated() / 1024**2
  print(f"Peak Memory used: {peak_mem:.2f} MB")
  ```

  This helps identify if a certain operation uses excessive memory. For instance, attention layers use memory quadratic in sequence length; if you set `input_size` too large, that might be the culprit. Or maybe your validation step is using a huge batch by default – you might then set `valid_batch_size` to something smaller to alleviate that (Nixtla allows specifying a separate validation batch size).

* **Batching inference:** In production, after training, you usually forecast relatively few series (maybe just BTC). Memory isn’t a big issue for inference on one series, but if you were forecasting many series in one go, note that NeuralForecast’s `predict()` can handle batches of series as well. If you ever do need to forecast thousands of series on a GPU, you’d want to batch them appropriately and possibly use half precision or do it in chunks to avoid OOM. Nixtla’s `inference_windows_batch_size` could be set so that the prediction is done in parts rather than all at once. For a single series, this is not a concern; it will be very fast anyway.

* **Gradient Checkpointing:** For extremely deep models (not usually the case here; PatchTST or TFT aren’t super deep in layers, just maybe wide), you can use gradient checkpointing to trade computation for memory. It means not storing intermediate activations and recomputing them in backward pass. Lightning allows it by setting `model.training_type_plugin` or manually in model code via `torch.utils.checkpoint`. If memory becomes a barrier (like training a massive transformer), this is an advanced option to enable.

* **CUDA optimization flags:** Ensure `torch.backends.cudnn.benchmark = True` (Lightning usually enables this by default when input sizes are not variable) for faster conv/RNN operations. This finds the optimal kernel for your GPU for conv layers (used in TCN or maybe LSTM internals).

* **GPU vs CPU for certain parts:** Sometimes data loading or processing can bottleneck. Nixtla’s DataLoader will load data on CPU and then move batches to GPU. If your data is very large or you do heavy augmentations, ensure the data loader is using multiple workers to feed GPU quickly (`num_workers` param in dataloader\_kwargs). If the GPU isn’t fully utilized (observing low usage in nvidia-smi), increase `num_workers` to, say, 4 or 8 to speed up data feeding. Also avoid heavy Python-side transforms in the training loop – precompute features as much as possible (which we did by computing technical indicators beforehand).

* **Scaling to larger GPUs:** With a 16GB or 24GB GPU, you can try more **multi-variate modeling** (like forecasting many related series together) or using larger contexts. For instance, you could attempt to forecast Bitcoin and Ethereum jointly with a multivariate model (Nixtla’s `MLPMultivariate` or others) if you have more memory. Or use a deeper TFT with more attention heads. Always verify that the added complexity yields accuracy gains – often there are diminishing returns.

* **Latency considerations:** If deploying the model for real-time inference (discussed in section 8), consider using **ONNX** or TorchScript to optimize the forward pass. ONNX can sometimes optimize away some overhead and run the model faster on CPU. Also, for transformers, libraries like ONNX Runtime or TensorRT can give big speed-ups for inference by optimizing the computation graph. On GPU, if you stick to PyTorch, enable `torch.backends.cudnn.benchmark` and possibly `torch.backends.cuda.matmul.allow_tf32 = True` (if using Ampere GPUs, TF32 speeds up matrix ops with minimal precision loss, and Lightning sets this by default in 16-bit mode).

In summary, with an 8GB GPU focus on **mixed precision** and **reasonable batch sizes** to fit memory. Use **gradient accumulation** if needed for larger effective batch. As you move to 16GB+, you can comfortably increase batch or model size, but always monitor usage. These optimizations ensure you utilize your hardware fully, reduce training time, and avoid out-of-memory crashes.

*(Side note: Nixtla’s tutorials often mention using Google Colab GPUs which are around 16GB; so their defaults are somewhat conservative for broad compatibility. You might push those limits on a dedicated 24GB GPU server.)*

## 7. Ensembling with MLForecast and StatsForecast

**Ensembling** combines multiple models’ forecasts to produce a more robust prediction than any single model. This is especially beneficial in volatile domains: different model types may capture different aspects of the series (e.g., a neural model might capture complex nonlinear patterns, while a statistical model might nail the basic trend/seasonality, and a tree-based model might incorporate exogenous signals well). We will discuss how to ensemble Nixtla’s NeuralForecast models with MLForecast (for machine-learning models like gradient boosting) and StatsForecast (for classical models), as well as ensemble weighting strategies and implementation.

* **Candidate Models to Ensemble:**

  1. *Neural models (NeuralForecast):* e.g. PatchTST, TFT, LSTM as discussed.
  2. *Statistical models (StatsForecast):* e.g. ARIMA, Exponential Smoothing, Theta method, Seasonal Naïve.
  3. *Machine Learning models (MLForecast):* e.g. Gradient Boosting (XGBoost/LightGBM), Random Forest, or even linear regression on lag features.

  By combining these, you hedge against each model’s weaknesses. For instance, an ARIMA might handle linear trends well but miss nonlinearities; a neural net might handle nonlinear patterns but sometimes overshoot; a gradient boosting model might excel at using exogenous features but be less good at long-term trends. The ensemble can incorporate all these strengths.

* **Dynamic vs Static weighting:**

  * *Static weights:* simplest approach – determine fixed weights for each model’s forecast (e.g., 50% neural, 30% ML, 20% stat) and use those for all future predictions. You might set these based on validation RMSE (inverse MSE weighting) or domain intuition. For example, if ARIMA performs almost as well as the neural model, you might give them comparable weights.
  * *Dynamic weights:* adjust weights over time or regimes. This can substantially improve performance if, for example, one model is known to perform better in high-volatility periods and another in low-volatility. Some strategies:

    * **Error-based adaptation:** Continuously calculate recent forecast errors for each model and adjust weights inversely proportional to error. For instance, at time \$t\$, look at the last N predictions each model made (on a rolling validation set or recent actuals) – compute say the 7-day MAPE for each model. Then set weight ∝ 1/MAPE (so lower error -> higher weight). Normalize weights to sum to 1. This way, if Model A has been more accurate than Model B in the past week, the ensemble will trust A more for tomorrow. This can be done in a rolling manner. One must guard against very short-term fluctuations (maybe smooth the errors with some decay factor).
    * **Regime classification:** Determine regime (perhaps using volatility or trend indicators as discussed in section 9). For example, classify each day as “trending” vs “choppy”. You might find that during strong trends, a momentum-based model (like a neural net that picked up the trend or even a simple naive model which just continues the trend) might outperform a mean-reversion model (like ARIMA). Conversely, in sideways markets, ARIMA or a mean-reverting model might do better. So you could set: *if regime=trending, weight neural model 0.7 and ARIMA 0.3; if regime=sideways, weight ARIMA 0.7, neural 0.3*, etc. This requires a reliable regime detection mechanism (e.g., ADX indicator or recent volatility magnitude). It essentially becomes a **switching ensemble**.
    * **Meta-learner:** Train a second-level model to output weights or forecast corrections. For instance, train a simple linear regression where input features are the individual model forecasts (and possibly other info like regime indicators) and the target is the actual value. This regression learns how to combine model outputs. If linear with non-negativity constraint, it’s similar to finding optimal weights (akin to OLS blending or stacking). For example, you could take historical validation forecasts from PatchTST (P), ARIMA (A), and LightGBM (L) and train a regression: \$\text{Forecast}\_\text{ens} = w\_P P + w\_A A + w\_L L\$ that minimizes MSE on the validation set. That yields static optimal weights under squared error. Or train a more complex model (even a small neural net or tree) to possibly produce different weights in different conditions (though careful not to overfit – simpler is usually better for blending).

    Nixtla’s ecosystem doesn’t have a dedicated ensembling function that automates dynamic weighting, but you can implement this logic externally with pandas/numpy once you have the forecasts.

* **Combining probabilistic and point forecasts:**
  If one model provides full probability distribution (e.g., quantiles from PatchTST or simulations from DeepAR) and another only gives point forecasts (e.g., ARIMA by default or an ML model), you have a few options:

  * Convert everything to point forecasts (like mean or median) and ensemble those, then handle uncertainty separately.
  * Or attempt to combine distributions: e.g., ARIMA can actually provide prediction intervals if you retrieve its variance. StatsForecast’s ARIMA model likely can return lower/upper bounds for a given confidence level. MLForecast models usually only give point estimates, but you could quantify their residuals to get uncertainty.
  * A pragmatic approach: ensemble the point forecasts to get an ensemble point estimate, and for the interval, use one model’s interval widened a bit or some combined quantile.

    * Example: If PatchTST outputs 90% interval \[low\_P, high\_P] and ARIMA outputs \[low\_A, high\_A], a combined 90% interval could be a weighted average of the bounds. However, averaging quantiles is not strictly proper (the result may not correspond to a valid distribution). But for a rough interval, you could do: low\_ens = \$\alpha \* low\_P + (1-\alpha)\* low\_A\$, high\_ens = \$\alpha \* high\_P + (1-\alpha)\* high\_A\$. Or take the min of lows and max of highs to be conservative (this would likely over-cover).
    * Alternatively, if you have a meta-learner as above, you can produce quantiles by simulating many forecasts: sample from each model’s distribution and then combine or pick quantiles across simulations. For instance, generate 1000 sample paths from PatchTST (if it produces quantiles you can do an approx distribution or if DeepAR you can directly sample) and 1000 from ARIMA (simulate from ARIMA’s Gaussian assumption). For each simulation, average the two (with some weight) to get an ensemble sample. Then quantiles of these ensemble samples give the prediction interval. This is effectively a form of **Bayesian model averaging** by simulation. It’s computationally heavier but doable offline.
    * Simpler: If one model is clearly better at uncertainty (maybe PatchTST trained for quantiles), you might rely on that model’s quantiles and just adjust the median via ensemble. For example, say PatchTST median is 50k and ARIMA forecast is 52k, ensemble median is 51k. For interval, you could take PatchTST’s interval and shift it by the difference (1k) or scale it slightly. This is ad-hoc but sometimes used (i.e., combine means, keep the more sophisticated model's spread).
  * In many cases, ensembling is focused on the point forecast. You can then derive prediction intervals by taking the ensemble residuals. For instance, after generating ensemble predictions on validation, compute the error distribution and set intervals such that 90% of errors lie within the bounds. This yields an empirical prediction interval that covers both model uncertainties and diversity. E.g., if ensemble error std is \$\sigma\$, you might set interval = ensemble forecast ± \$z\_{0.95} \* \sigma\$.

  **Dynamic weighting and uncertainty:** If weights change, uncertainty calculation becomes even trickier, because effectively your ensemble distribution is a mixture of model distributions with time-varying weights. But as a practical step, focus on getting a good ensemble point forecast first; then calibrate intervals from that.

* **Real-time ensemble adjustment (code example):**
  Let's say you have three models: `nf_pred` from NeuralForecast (e.g. PatchTST), `sf_pred` from StatsForecast (e.g. ARIMA), and `ml_pred` from MLForecast (e.g. LightGBM). Each is a pandas DataFrame with columns `ds` and a forecast (maybe named after the model). You need to ensure they cover the same forecast horizon and align on `ds`. You can merge them:

  ```python
  ens_df = nf_pred.merge(sf_pred, on=['unique_id','ds']).merge(ml_pred, on=['unique_id','ds'])
  # Now ens_df has columns like 'PatchTST', 'AutoARIMA', 'LGBM' for forecasts.
  ```

  Now suppose you have precomputed weights `w_p, w_a, w_m`. A static example:

  ```python
  w_p, w_a, w_m = 0.5, 0.3, 0.2
  ens_df['ensemble'] = w_p*ens_df['PatchTST'] + w_a*ens_df['AutoARIMA'] + w_m*ens_df['LGBM']
  ```

  This yields the ensemble forecast. For dynamic weights, assume you have recent actuals up to `t0` and you are forecasting from `t0` onward:

  ```python
  # Pseudocode for error-based dynamic weighting:
  recent_actuals = actual_df[actual_df['ds'] > t0 - pd.Timedelta(days=7)]
  # assume you have stored recent predictions from each model in this period
  recent_preds_nf = ...
  recent_preds_sf = ...
  recent_preds_ml = ...
  # Compute, say, mean absolute error for each model in last 7 days:
  mae_nf = np.mean(np.abs(recent_preds_nf['y_hat'] - recent_actuals['y']))
  mae_sf = np.mean(np.abs(recent_preds_sf['y_hat'] - recent_actuals['y']))
  mae_ml = np.mean(np.abs(recent_preds_ml['y_hat'] - recent_actuals['y']))
  inv_errors = [1/mae_nf, 1/mae_sf, 1/mae_ml]
  w_p, w_a, w_m = [x/ sum(inv_errors) for x in inv_errors]
  # Then use these w_p, w_a, w_m in the ensemble formula above.
  ```

  If one model had double the accuracy (half the MAE) of others recently, it will get \~2x the weight.

  For *regime-based*, e.g.:

  ```python
  if current_volatility > threshold:
      w_p, w_a, w_m = 0.6, 0.2, 0.2  # trust neural (which maybe is more reactive) more in high vol
  else:
      w_p, w_a, w_m = 0.4, 0.4, 0.2  # in stable regime, ARIMA and neural equal
  ```

  This requires you to compute `current_volatility` or regime label from recent data (could be std of returns last 24h or an indicator like ATR).

* **Ensembling in Nixtla’s frameworks:** Nixtla doesn’t have a one-click ensemble, but you can leverage each library’s output easily since they all produce pandas DataFrames with `unique_id` and `ds`. For example:

  * **StatsForecast usage:**

    ```python
    from statsforecast import StatsForecast
    from statsforecast.models import AutoARIMA, ETS
    sf = StatsForecast(df=train_df, models=[AutoARIMA(season_length=24), ETS(season_length=24)], freq='H')
    sf.fit()  # fits the models
    # Forecast h steps
    sf_forecast = sf.forecast(h=24, level=[])  # level can specify confidence intervals
    sf_forecast.head()
    ```

    StatsForecast will return a DataFrame with columns `AutoARIMA` and `ETS` (plus `unique_id` and `ds`). If you only use one model in StatsForecast, it returns just that model’s forecasts in a column.
    For example, `sf_forecast[['unique_id','ds','AutoARIMA']]` gives ARIMA predictions.

  * **MLForecast usage:**
    MLForecast is for machine learning regressors on lags and features:

    ```python
    from mlforecast import MLForecast
    from sklearn.ensemble import RandomForestRegressor
    mlf = MLForecast(
        models = [RandomForestRegressor(n_estimators=100)],
        lags = [1,24,168],  # example lags (1h ago, 24h ago, 7 days ago)
        date_col='ds', id_col='unique_id', target_col='y',
        freq='H'
    )
    mlf.fit(train_df, static_df=None)  # no static features in this example
    ml_forecast = mlf.predict(horizon=24, dynamic_dfs=None)
    ml_forecast.head()
    ```

    `ml_forecast` will likely be a DataFrame with columns `unique_id, ds, RandomForestRegressor` (the column name might be something like `RandomForest` or a generic name if not provided). You could rename it for clarity. MLForecast handles creating lag features and any transformations for you, based on what you provided. If you added extra features like indicators, you can pass them via `dynamic_dfs` for future values if needed, similar to futr\_exog.

  * **NeuralForecast usage (for ensemble):** We already saw using NeuralForecast. If you have multiple neural models, you can either:

    * Train them together via `NeuralForecast(models=[model1, model2, ...])` which will output forecasts for each model in one go. The result of `nf.predict()` will have columns for each model’s forecast, named by their `alias` if you set `alias='ModelName'` in each model. This is convenient to get all neural forecasts aligned. For example, if you do:

      ```python
      models = [PatchTST(..., alias='Patch'), LSTM(..., alias='LSTM')]
      nf = NeuralForecast(models=models, freq='H')
      nf.fit(train_df, val_df=val_df)
      Y_hat = nf.predict()  # forecast on horizon defined in models
      ```

      Then `Y_hat` will have columns 'Patch' and 'LSTM' along with 'ds' and 'unique\_id'. You can then ensemble those two easily. *(Note: When passing multiple models, NeuralForecast trains them sequentially by default, not truly in parallel, but it’s easier code-wise. Each model’s early stopping etc. works independently.)*
    * Alternatively, train them separately (maybe if you want to use different hyperparameters or datasets), then merge their forecasts like we did with StatsForecast/MLForecast outputs.

* **Cross-tool format conversion:** The primary thing is aligning the DataFrames by `ds` and `unique_id`. All Nixtla libs output in the **long format** with those keys, so merging is straightforward. One caveat: StatsForecast by default might not output the history, just future forecasts. If you want to do a backtest via StatsForecast’s `cross_validation`, it will output actuals and forecasts similarly. The ensemble weighting approach might require having actuals and predictions together for a certain period – you can achieve this by doing backtesting with each tool:

  * `nf.cross_validation()` for neural models,
  * StatsForecast’s `cross_validation()` (StatsForecast has a similar function taking cutoff points),
  * MLForecast’s `cross_validation` or you manually simulate it because MLForecast may not have a built-in CV (but you can loop).
    Then join those backtest predictions. This is more involved, but it provides a dataset of predictions vs actuals for each model across time, from which you could train a meta-learner or dynamic weighting scheme.

Another point is **frequency and index alignment**: Make sure all models use the same `freq='H'` (hourly) and that your DataFrame is complete (no missing hours). StatsForecast will assume an implicit frequency and if there are gaps in `ds`, it might mis-handle them (e.g., ARIMA expects regular intervals). Fill any missing timestamps in the history (with NaN or imputed values) or drop them and ensure to specify `freq`. When merging forecasts, if one model’s output is missing a timestamp that others have, it implies something went wrong (like one model forecast a different horizon or missed a period due to training data shorter). Always double-check the forecast `ds` range matches. If needed, use an inner merge (which we did) to only ensemble where all models have a prediction.

* **Example ensemble scenario:** Suppose:

  * PatchTST predicts a sharp rise (maybe it recognized a bullish pattern),
  * ARIMA predicts a slight increase (carrying forward recent trend but damping it),
  * LightGBM (with technical indicators) predicts a moderate rise.
    An ensemble might take a weighted average to get a more balanced forecast. If PatchTST was over-enthusiastic, ARIMA can pull it down a bit. Conversely, if ARIMA misses a regime change, the ensemble weight on PatchTST or LightGBM (which might catch nonlinear signals) ensures the ensemble isn’t too conservative.

In practice, ensembling often yields more stable and lower error forecasts. Jan Gasthaus (Amazon) noted that “an ensemble of statistical models can outperform individual deep learning models and is only slightly outperformed by an ensemble of deep models”. This highlights that even simple models, when combined, can challenge a fancy model. For production, an ensemble also provides resilience: if one model fails or is off in a certain scenario, others can compensate.

* **Real-time adjustment and monitoring:** If you implement dynamic weighting, this effectively becomes part of your inference pipeline. Each time you forecast, you’d update model weights based on recent performance. This can be automated – e.g., after each day’s actuals are in, compute errors and update weights for the next day’s forecast. This blur the line between training and inference a bit, but it’s manageable. Keep a buffer of recent errors for each model.

* **Cross-library nuances:** If you want to **convert a StatsForecast model into NeuralForecast or vice versa**, note they often have analogous versions (e.g. ARIMA is only in StatsForecast, but simple baselines like Naïve or SeasonalNaïve might also exist in NeuralForecast as a model class). You could theoretically include a naive model as one of the NeuralForecast `models=[Naive()]` and train alongside the neural net, then you could ensemble within NeuralForecast’s output. But StatsForecast’s implementation of ARIMA is likely more optimized than writing your own ARIMA. So it's fine to use separate libs and combine outputs.

* **Ensemble code example (full pipeline):**

```python
# Train NeuralForecast model(s)
nf = NeuralForecast(models=[PatchTST(..., alias='Patch')], freq='H')
nf.fit(train_df, val_df=val_df)
nf_future = nf.predict()  # next 24h, for example

# Train StatsForecast model
sf = StatsForecast(df=train_df, models=[AutoARIMA(season_length=24)], freq='H')
sf.fit()
sf_future = sf.forecast(h=24)
# sf_future has 'AutoARIMA' column

# Train MLForecast model
from lightgbm import LGBMRegressor
mlf = MLForecast(models=[LGBMRegressor(n_estimators=100)], lags=[1,24,168],
                 date_col='ds', id_col='unique_id', target_col='y', freq='H')
mlf.fit(train_df)
ml_future = mlf.predict(horizon=24)

# Merge forecasts on ds
future_df = nf_future.merge(sf_future, on=['unique_id','ds']).merge(ml_future, on=['unique_id','ds'])
# Assume columns: 'Patch', 'AutoARIMA', 'LGBMRegressor'
# Compute weights (static or dynamic)
w_p, w_a, w_l = 0.5, 0.3, 0.2  # example static weights
future_df['ensemble'] = w_p*future_df['Patch'] + w_a*future_df['AutoARIMA'] + w_l*future_df['LGBMRegressor']
```

Now `future_df[['ds','ensemble']]` gives the ensemble forecast trajectory. You can then plot or evaluate it. If doing dynamic, replace the weight assignment with a function that computes `w_p, w_a, w_l` based on recent data as discussed.

* **Real-time ensemble weight adjustment:** If you deploy this ensemble, you might implement a routine that runs daily:

  1. At end of day, gather actual price for that day.
  2. Compute each model’s error for that day (or a window).
  3. Update weights accordingly.
  4. Use updated weights for tomorrow’s forecast.

  This becomes a feedback loop. It’s wise to include some smoothing (e.g., use a rolling average of errors over 3-7 days rather than just yesterday, to avoid one bad day swinging weights too much).

* **Cross-tool format conversion considerations:**

  * Ensure consistent index types (pandas Timestamp vs Python datetime). Usually fine if using pandas throughout.
  * If using Polars or Dask for large data, the merging concept remains but might need adjustments (Nixtla supports Polars too).
  * If you needed to ensemble probabilistic forecasts: Nixtla’s frameworks often produce quantiles with column names like `model-lo-90`, `model-hi-90` for lower/upper 90% intervals. You would merge those similarly if needed.

By following these steps, you leverage the strengths of each forecasting approach. The end result is a more **robust ensemble** that is likely to outperform any individual model on average. It also adds a layer of safety: if an unexpected scenario breaks one model’s assumptions, at least one of the other models might handle it better, and the ensemble won’t be completely misled.

## 8. Deployment Considerations (Saving, Loading, FastAPI, Docker, Monitoring)

Building a model is half the battle – deploying it in a reliable, efficient manner is the other half. Here we cover how to save and load Nixtla models, exporting to formats like ONNX, serving forecasts via FastAPI, containerizing the solution, and monitoring it in production.

* **Model Saving & Loading:** Nixtla’s NeuralForecast provides built-in methods to persist models:

  * `nf.save(path, overwrite=True, save_dataset=True)` saves the trained model(s) to disk. This will likely save a checkpoint with model weights, architecture, and possibly scaling information. The `save_dataset` flag if True may include normalization parameters or last seen values (useful for things like normalizing transformations).
  * Correspondingly, `NeuralForecast.load(path)` will reload the model(s). This means you can train offline and then load the ready model in a production environment without re-training. For example:

    ```python
    nf.save('./models/btc_patchtst/', overwrite=True)
    # ... later or on another machine:
    nf2 = NeuralForecast.load('./models/btc_patchtst/')
    Y_hat = nf2.predict()  # use loaded model for forecasting
    ```

    Ensure the path contains the files saved. Nixtla might save one file per model or a zip of everything.
  * If you train multiple models in one NeuralForecast, `save` will save all of them by default. You can specify `model_index` to save only a specific one. For simplicity, keep them together if you plan to always use them as a group.
  * For StatsForecast and MLForecast models: StatsForecast’s models (like ARIMA) are usually pickle-able or have their own save methods (AutoARIMA likely can be pickled as it stores the fitted statsmodels or pmdarima object). You might manually pickle the StatsForecast object:

    ```python
    import pickle
    with open('arima_model.pkl','wb') as f:
        pickle.dump(sf, f)
    # and later:
    sf = pickle.load(open('arima_model.pkl','rb'))
    ```

    However, note that StatsForecast class holds the whole dataset as well. A better approach is to save the fitted model parameters: for example, the `AutoARIMA` model inside `sf` might have an attribute `.model_` or similar with the fitted statsmodels ARIMA results which can be saved. Check StatsForecast documentation for saving models. Alternatively, simply store the forecasts or retrain quickly on load (ARIMA can be slow to retrain though).
    MLForecast models (like an sklearn model) can be pickled or saved via their own methods (LightGBM has `model.booster_.save_model('lgb.txt')`, XGBoost has `save_model`, or just pickle the MLForecast object – but that includes data transformation state as well).
  * **Versioning:** When saving models, consider using MLflow or similar to version them. Nixtla has a tutorial on MLflow integration – they show how to use `mlflow.pytorch.autolog()` to log models and parameters. Autologging can capture the PyTorch model (NeuralForecast uses PyTorch Lightning which integrates with MLflow). The code snippet indicates:

    ```python
    mlflow.pytorch.autolog(checkpoint=False)
    with mlflow.start_run() as run:
        dataset = mlflow.data.from_pandas(Y_df, source="AirPassengersDF")
        mlflow.log_input(dataset, context="training")
        mlflow.log_params(model_params)
        models = [NBEATSx(**model_params)]
        nf = NeuralForecast(models=models, freq='M')
        nf.fit(Y_df=Y_df)
        mlflow.pytorch.get_default_conda_env()
        # ... after training
    mlflow.pytorch.autolog(disable=True)
    nf.save(path='./checkpoints/test_run_1/')
    ```

    This logs the data and params to MLflow, and you could retrieve the model by loading the saved checkpoint or via MLflow’s model registry. Using MLflow is great for tracking experiments and deploying the exact model later (e.g., MLflow can serve models as a REST API, although for forecasting you might integrate it manually).
  * **Transfer learning / fine-tuning:** The save/load also helps if you want to fine-tune later. You can load the model weights into a new NeuralForecast instance and continue training on new data (Lightning will treat it as resuming training if you call `.fit()` after loading). Ensure to use the same architecture when loading (the model class and hyperparams must match or be saved as part of checkpoint).

* **Export to ONNX / TorchScript:** If you need to deploy in a language/platform outside Python (or simply want faster inference on CPU), converting the model can be beneficial:

  * NeuralForecast models are PyTorch under the hood (LightningModule). You can likely extract the underlying `nn.Module` via something like `nf.models[0]` (which might be a wrapper containing the model) or an attribute `model` inside it. In Nixtla’s GitHub, it seems each model class might hold the actual network in an attribute, or itself be the LightningModule.
  * Once you have the `torch.nn.Module`, you can do:

    ```python
    dummy_input = torch.rand(1, input_size + len(lags_exog), device='cpu')  # shape should match model's expected input
    torch.onnx.export(model, dummy_input, "model.onnx", opset_version=12)
    ```

    The tricky part is constructing the dummy input of correct shape: for example, PatchTST expects a batch of shape (N, input\_length, channels). If univariate and no exog, channels=1. If you have exog, channels includes them if the model expects them concatenated; more likely, the model might have separate input branches for exog. You may need to adapt the input or export the entire LightningModule which handles exog internally. Lightning might have a method `model.to_onnx("model.onnx", input_sample=(X, futr_exog_tensor))`. The Nixtla team pointed to a Lightning GitHub issue for ONNX, implying one can use Lightning’s built-in export:

    ```python
    trainer = pl.Trainer(...)
    trainer.save_checkpoint('model.ckpt')
    pl_module = PatchTST.load_from_checkpoint('model.ckpt')
    input_sample = (torch.randn(1, input_size, 1), torch.randn(1, horizon, exog_dim))
    pl_module.to_onnx("model.onnx", input_sample=input_sample)
    ```

    This is conceptual; adjust input\_sample to exactly how the model’s forward is defined. ONNX export might fail if the model uses some unsupported operations, but most PyTorch ops in forecasting models are standard (linear layers, conv, attention).
  * ONNX allows deploying the model in C++ or loading in frameworks like ONNX Runtime, which can run the model very fast on CPU (often faster than PyTorch for single inference due to optimizations like fused ops). ONNX Runtime can also accelerate on GPU and even quantize to int8 for further speed if needed.
  * **TorchScript:** Alternatively, you can do `scripted = torch.jit.trace(model, dummy_input)` to get a TorchScript, then `scripted.save("model.pt")`. TorchScript can be loaded in a C++ environment with LibTorch if needed. It’s less portable than ONNX but sometimes simpler if staying in PyTorch. For a simple deployment scenario (Python API), you may not need ONNX at all; you can just load the PyTorch model and use it.
  * If ONNX conversion is problematic (due to dynamic axes or something), and your deployment is still Python, you might skip it. But if you plan to serve via a REST API in Python, often the overhead of Python is bigger than model compute, so ONNX might not be necessary unless you want multi-threaded C++ speed or to integrate with other languages.

* **FastAPI for Serving Forecasts:** FastAPI is a popular Python web framework for building RESTful APIs. You can wrap your forecasting logic in an API to get predictions on demand. Some considerations:

  * **Loading the model:** Load your saved model at app startup to avoid re-loading on each request. In FastAPI, you can do:

    ```python
    from fastapi import FastAPI
    app = FastAPI()
    # Load model globally
    nf_model = NeuralForecast.load('./models/btc_patchtst/')
    # If using ensemble, maybe load other models or any artifacts.

    @app.get("/forecast")
    def get_forecast(h: int = 24):
        # prepare input for forecast: e.g., the model might automatically use its internal state to forecast next h.
        # If exogenous features are needed for future (like day-of-week flags):
        future_dates = pd.date_range(start=last_timestamp + pd.Timedelta(hours=1), periods=h, freq='H')
        futr_df = pd.DataFrame({'unique_id': 'BTC', 'ds': future_dates})
        futr_df['dow'] = futr_df['ds'].dt.dayofweek
        futr_df['hour'] = futr_df['ds'].dt.hour
        # (Add sin/cos if model expects those.)
        forecast_df = nf_model.predict(futr_df=futr_df)
        # forecast_df will contain ds and model forecast columns
        results = forecast_df[['ds', 'Patch']].rename(columns={'Patch':'y_hat'})
        return results.to_dict(orient="records")
    ```

    This is a simple GET endpoint that returns the next `h` hours forecast. We assumed the model name alias 'Patch' for the forecast column. Adjust accordingly (if single model, it might name column after model class by default).
  * You might want to support POST with a JSON body if user wants to supply some custom inputs (like "here is new data to update the model" or different horizon per call).
  * **Live data updates:** The above assumes the model has seen data up to `last_timestamp`. In a live setting, you must feed the latest actual price to the model before forecasting the next hours. If using NeuralForecast’s state, you might need to call `nf_model.fit(new_df)` or better, maintain a data structure. Perhaps simpler: store the time series in a database or in memory; when a request comes in, fetch the latest `train_df` (or just the last window needed) and call `nf_model.predict`. If your model was trained up to T, and now you have data up to T+n, ideally you should retrain or update it. Perhaps you retrain nightly and in between just use the last trained model for intraday forecasts.
  * *Alternative approach:* Use the model to predict incremental steps as new data arrives (like a rolling prediction). If using an RNN or DeepAR that maintains state, you could update state with new actuals (some frameworks allow feeding actuals to update hidden state without gradient updates). NeuralForecast doesn’t explicitly expose that, so likely you retrain or re-fit periodically rather than per tick. So design the API maybe to produce forecasts after each retraining (like once per day).

  If you want immediate updates after each new hour’s actual, you could integrate a background job or use something like **online learning** (see section 10 on partial retraining).

  * **Concurrency:** FastAPI with Uvicorn can handle multiple requests concurrently (especially if using async views, but here heavy lifting is the model which is CPU/GPU bound). If using a GPU model, be cautious: by default, PyTorch operations are asynchronous on GPU but will serialize in the driver if called from multiple threads. It’s often easiest to allow only one request at a time for GPU (or run multiple model instances). Alternatively, you could use `asyncio` to run predictions in an event loop but it won't actually parallelize on GPU. For CPU ONNX, you can enable multi-threading (ONNX Runtime uses threads).

  * If high throughput is needed (e.g., many forecast requests per second), you might consider running predictions on CPU in parallel, or scale horizontally (multiple replicas of the service). For a single series forecast, latency should be very low (few milliseconds) so the bottleneck is minimal.

  * **Security and validation:** Ensure the API validates input (like `h` not too large). Possibly limit horizon or implement authentication if needed.

* **Dockerization:** To containerize this service:

  * Use a base image with needed packages. For GPU deployment with PyTorch, use an official PyTorch CUDA image (e.g., `pytorch/pytorch:2.0.0-cuda11.7-cudnn8-runtime`). For CPU-only, use something like `python:3.10-slim` and install required libs (though for NeuralForecast you need PyTorch, which can be heavy but the CPU version).
  * Install Nixtla packages inside (e.g., `pip install neuralforecast==<ver> statsforecast mlforecast ta fastapi uvicorn[standard]` etc.). Pin versions for reproducibility.
  * Copy your code (the FastAPI app and any scripts).
  * Set an entrypoint to run Uvicorn server:

    ```Dockerfile
    CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "80"]
    ```

    (assuming your app is in `app.py` with `app = FastAPI()`).
  * If GPU needed, ensure to use `--gpus` flag when running the container (Nvidia Docker).
  * Make sure to expose the port (and in Dockerfile `EXPOSE 80`).
  * Keep the image lean: using slim base and installing only necessary dependencies. Remove dev tools if not needed after building (you might compile some stuff for FBProphet if you had it, but we don't use Prophet here).
  * Test the container locally before deploying to cloud.

* **Monitoring & Logging:**

  * **Logging:** instrument the code to log each request (FastAPI or Uvicorn logs by default requests). You can also log the forecast output or summary (maybe not every value, but things like “Forecast for next hour = X”). Use a structured logging library or at least print logs; in Docker these will go to stdout.
  * **Error tracking:** if any exceptions occur (e.g., model fails for some reason), catch and log them or use an error tracking service (like Sentry for Python).
  * **Performance metrics:** We want to monitor latency and resource usage.

    * For latency: FastAPI’s middleware or a simple time capture can measure how long each forecast takes and log it. You might push these metrics to Prometheus.
    * For GPU/CPU utilization: in a Kubernetes deployment, one could use DCGM exporter for GPU metrics or node exporter for CPU. Or integrate Nvidia’s Python bindings to get GPU mem usage inside app (e.g., using `torch.cuda.memory_allocated()` in each request to see usage).
  * **Prometheus integration:** A convenient way is to use the `prometheus_client` library. For example:

    ```python
    from prometheus_client import Counter, Histogram
    req_counter = Counter("forecast_requests_total", "Total forecast API requests")
    latency_hist = Histogram("forecast_latency_seconds", "Forecast API latency")

    @app.get("/forecast")
    def get_forecast(h: int = 24):
        req_counter.inc()
        start = time.time()
        # ... [perform forecasting] ...
        latency = time.time() - start
        latency_hist.observe(latency)
        return results
    ```

    Also add an endpoint to expose metrics:

    ```python
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    @app.get("/metrics")
    def metrics():
        data = generate_latest()
        return Response(content=data, media_type=CONTENT_TYPE_LATEST)
    ```

    Then Prometheus can scrape the `/metrics` endpoint. You’d see metrics like `forecast_requests_total` increasing and a histogram of latencies.
  * **Model monitoring:** Over time, you should monitor forecast accuracy too (drift detection). For example, log the actual vs predicted for each time step somewhere. If using MLflow, you can log the forecasts and actuals as metrics or artifacts daily. Another approach: push these to a time-series database or just evaluate offline. This way you catch if model performance is degrading (e.g., due to a regime change), indicating need to retrain or adjust.
  * **Alerts:** You might set alerts if, say, forecast error exceeds a threshold (which could mean an anomaly the model didn’t predict) or if the service latency goes too high or if the model fails to produce output.

* **Scalability & Fault-tolerance:** If deploying in a container orchestration (K8s), run at least 2 replicas behind a load balancer for redundancy. If using a GPU, maybe one per GPU node unless multiple models can share one GPU (serving is usually light so multiple containers on one GPU is possible if not saturating it).

  * Consider warm starting: loading model on startup takes some time – ensure readiness probes account for that (i.e., don’t send traffic until model loaded). You might implement FastAPI `startup` event to load the model.
  * Memory leaks: ensure that each request doesn’t allocate huge memory without freeing. PyTorch might accumulate some cache; calling `torch.cuda.empty_cache()` isn’t usually needed unless you dynamically allocate new models. If you simply reuse the loaded model, memory usage should be stable.

* **ONNX and FastAPI**: If you exported to ONNX and want to use that for inference:

  * Use `onnxruntime` in Python: load the onnx model in an `InferenceSession`:

    ```python
    import onnxruntime as ort
    ort_session = ort.InferenceSession("model.onnx")
    outputs = ort_session.run(None, {input_name: input_array.astype(np.float32)})
    ```

    You must provide the input as numpy array(s). This is a bit complicated if your model has multiple inputs (like separate arrays for past target vs future exog). You need to name them as in the ONNX graph. You can find input names via `ort_session.get_inputs()`.
    ONNX Runtime on CPU can be faster than PyTorch CPU especially for large models or many requests, but for a single series forecast the difference might be small. On GPU, PyTorch vs ORT are similar since overhead is minimal.

* **Docker with MLflow or Bentoml:** Another approach to deployment is using MLflow’s model serve or BentoML, which package the model and server. But since our scenario is custom (with ensembling and possibly data preprocessing), a custom FastAPI app is fine.

**Summary of deployment tips:**

* Save the trained model (and any scaler/feature metadata) and use the saved version in production to ensure consistency.
* Test loading the model and running a forecast offline (to catch any issues with dependencies or environment).
* Containerize with all dependencies (PyTorch, Nixtla libs, etc.) and ensure the container is reasonably sized by using slim images.
* Provide an API (FastAPI) for prediction requests, possibly with adjustable horizon.
* Preload model and any large data to avoid per-request overhead.
* Use ONNX if you need cross-platform or slight performance gains on CPU.
* Monitor the service for performance (latency, throughput) and accuracy drift. Use Prometheus for metrics and possibly Grafana for dashboards.
* Implement logging and error handling so that any failures in the forecasting process (e.g., if the model somehow encounters an edge case like NaN input) are visible and don’t crash the server ungracefully.

By following these practices, you’ll have a **production-ready forecasting service** that is robust and maintainable. This service can be queried for the latest BTC price predictions and can be integrated into a larger system (e.g., a trading bot or a dashboard). And with Docker/Kubernetes, you can scale it or redeploy updated models easily.

## 9. Bitcoin-Specific Adjustments (Regime detection, Transformations, Extremes)

Forecasting Bitcoin prices requires accounting for the unique characteristics of crypto markets: regime shifts, high volatility, 24/7 trading, and occasional extreme events. We discuss several adjustments and techniques to tailor the forecasting system to these challenges:

* **Market Regime Detection:** Bitcoin (and crypto in general) experiences different regimes – bullish trends, bearish trends, high-volatility consolidation, low-volatility stagnation, etc. Identifying the current regime can inform both the modeling approach and the ensemble weighting (as mentioned in section 7). Some ways to detect regimes:

  * **Volatility-based:** Calculate the rolling volatility (standard deviation of returns) or ATR (Average True Range) over a window (say 7 days). Define thresholds: e.g., if 7-day vol > X%, call it a high-vol regime; if < Y%, call it low-vol. Volatility regimes might affect which model is best (a mean-reverting model might fail in high vol trending markets).
  * **Trend-based:** Use a measure like ADX (Average Directional Index) – it quantifies trend strength regardless of direction. High ADX = strong trend (regime trending), low ADX = no clear trend (range-bound regime). If ADX is high and +DI > -DI (part of ADX calculation), that’s a strong uptrend; if high and -DI > +DI, strong downtrend.
  * **Return sign analysis:** Simpler – count how many of the last N days were up vs down, or the cumulative return over N days. If cumulative return is > some threshold, you’re in a bullish swing, if < -threshold, bearish swing. If near zero, sideways.
  * **Machine learning**: One could cluster historical periods by volatility and return profile to categorize regimes (e.g., K-means on features like 7-day vol and 7-day return could cluster into “bull high vol”, “bear high vol”, “low vol” clusters).

  Once you have a regime label, you can:

  * Adjust ensemble weights (as previously discussed).
  * Switch models or hyperparameters: e.g., perhaps maintain two versions of the model, one trained on bull markets only and one on bear markets only, and choose which to use based on current regime classification. This is like a regime-switching forecasting model. It’s advanced and requires confidence in regime detection accuracy (misclassification can hurt).
  * Include regime as an input feature to the model: e.g., an indicator feature “is\_bull\_market” (could be static or slowly changing). The model could then internally adjust its predictions if that feature is on. TFT could use it as a static exogenous to condition forecasts.

  **Volatility handling strategies:**

  * If in a high-volatility regime, you might want to predict a wider range of outcomes and be more cautious. Practically, you might widen prediction intervals (some forecasting systems multiply predicted variance by a factor in volatile periods).
  * Possibly shorten forecast horizon or update more frequently in high vol times, since uncertainty grows faster.
  * Use models that inherently adapt to volatility: e.g., a GARCH model for volatility can forecast next period’s volatility. StatsForecast has a `GARCH` model listed. You might not directly care to forecast volatility, but you could use it to adjust interval width or combine it with a price forecast (e.g., a two-step model: forecast return = 0 plus a volatility forecast for intervals).

* **Log Transformations:** A common preprocessing for price series is using log prices or returns instead of absolute price. This is because:

  * Price series are non-stationary (generally trending upward over years, with multiplicative growth). Log transform makes multiplicative changes (percent changes) more linear additive. Many models (like ARIMA, neural nets) perform better if the underlying series is stationary.
  * Log differences (returns) often have more stable variance over time (no guarantee, but often better than raw prices).
  * If you forecast in log terms, you are effectively forecasting the percentage change. For example, a forecast of +0.02 in log translates to \~+2% price move.

  How to implement:

  * Take \$\tilde{y} = \log(y)\$ for the price. Train your model(s) on \$\tilde{y}\$ as target. If you have exogenous like volume, you may also log-transform volume (since volume has right-skew).
  * After forecasting \$\tilde{y}*{t+h}\$, convert back: \$\hat{y}*{t+h} = \exp(\hat{\tilde{y}}\_{t+h})\`.
  * For point forecasts, you can directly exponentiate the prediction (which gives median forecast in price if \$\tilde{y}\$ was median in log under symmetry assumptions – slight nuance: if distribution of log forecast is symmetric, the median log forecast exponentiated is the median of price, but not exactly the mean of price. Often we care about median so that's fine).
  * For intervals: if you predicted quantiles in log-space, exponentiate each quantile to get interval in price space. This will yield asymmetric intervals (which is good, since uncertainty to upside vs downside can differ proportionally).
  * Note: If using a model like ARIMA, working in log can be done by taking logs, modeling, then exponentiating forecasts (with a small adjustment for bias if you want mean forecast – usually we just do median).
  * Many analysts prefer to forecast returns (difference of log) rather than price, then accumulate. For daily or hourly, forecasting return can be easier to get stationarity. But if you forecast returns and then integrate to price, small errors accumulate. Instead, forecasting log price directly for a short horizon is simpler and captures the same idea without needing integration.
  * Nixtla's `TimeSeries Scaling` doc doesn’t explicitly mention log, because scaling is different from transforming. If you do log transform, you should not use the original scaling on top of that (since log already scaled things). Instead, either log-transform externally and feed into model with identity scaler, or incorporate it in modeling. Nixtla’s `RobustForecasting` tutorial uses Huber loss and did not specifically do log, but log could be seen as another way to reduce outlier impact (by compressing large values).

  **When log might not be ideal:** If the series can hit zero or negative (BTC can’t negative, but it could go to very low values theoretically, though practically unlikely to hit zero spontaneously). Log is undefined at zero. With BTC’s lowest in recent years being maybe in hundreds or thousands, log is fine (just ensure values > 0; if you had zeros you'd offset by a tiny amount).

* **Holiday/Weekend Treatment:** Bitcoin trades continuously, so there are no “holidays” where trading stops. However:

  * *Volume differences:* Weekends often see lower trading volumes in traditional finance; in crypto, weekends sometimes have lower volume but can also have outsized moves due to thin liquidity. It’s worth testing if day-of-week has a systematic effect on returns or volatility. You might find, e.g., that Sunday nights are quieter or Monday mornings have a volatility spike when institutional traders come back. Including day-of-week features (which we discussed adding as exogenous) will allow the model to capture any consistent pattern (if present).
  * *Holiday events:* Though markets don’t close, trader behavior around major holidays (e.g., Christmas, Chinese New Year) might change volumes or volatility. If you have a calendar of major holidays, you could include a binary feature for those days. For example, a “Dec 25th” flag might show lower movement historically (or maybe not – one would analyze historical Christmases). Similarly, include an “end of month” or “end of quarter” dummy if you suspect periodic cycles (some funds might do moves at month-end).
  * *Working hours:* Perhaps segment by hour of day and weekday vs weekend. We already include hour and weekend flags, which covers this. For instance, if the model sees an upcoming Sunday 3am, and historically that combination has a certain pattern (maybe mean reversion due to low activity), it can adjust the forecast lower volatility or drift accordingly.

  If after including these, you find no significant effect, it’s fine – at least the model had the opportunity to learn any if it existed.

* **Handling Extreme Price Movements:** Crypto can have “black swan” events (e.g., a 50% crash in a day like March 12, 2020, or a rapid doubling in a short time). These extremes pose several issues:

  * They can **distort model training** if treated as normal points. A neural net might try to fit that point and in doing so make other predictions worse (especially if using MSE loss which heavily penalizes that outlier – another reason we choose Huber). Even Huber will linearize the error for large residuals but it still can influence weights.

  * Strategies:

    * **Outlier detection & treatment:** Identify outliers in the historical data (e.g., points where daily return > 5 standard deviations from mean). You can either remove them from training or cap them. In robust statistics, sometimes you **winsorize** data: cap extreme values at some percentile. For instance, limit hourly returns to say +/- 30% during training to avoid extremely large values dominating. The model then won’t try to predict beyond that range since it never saw it. But then how to handle such events in forecasting? It might simply never predict them (which is arguably fine; these are inherently unpredictable events due to exogenous shocks).
    * **Explicit stress scenarios:** If you want the model to be aware that extreme moves can happen (for risk management), you might incorporate an exogenous indicator that spikes in known crash scenarios. But usually, crashes are unpredictable by definition (except maybe preceded by volatility rise, which the model would see via volatility features).
    * **Use a heavier-tailed distribution for forecasts:** If using probabilistic models, choose distributions like Student-t which give probability to extreme deviations. DeepAR with Student-t likelihood is an example – it will produce wider tails than a normal distribution, thus sometimes generating extreme samples.
    * **Anomaly detection overlay:** Use a separate mechanism to catch anomalies. For example, have rules: if price moves > X% in an hour, flag as anomaly (since model likely didn’t predict that). In production, you might want to override the model in such cases or at least flag that “this forecast is likely invalid now because an unforeseen event occurred.” After a huge move, you’d retrain the model including that data point so it can adjust to the new level.

  * **Rescaling after a crash:** If the price drops from 60k to 30k in a day, a model trained on pre-crash data might be biased (e.g., if not using RevIN or such, it might still expect a reversion to higher mean). Techniques like RevIN help because each window is normalized. Also, retraining quickly after a regime shift is crucial to recalibrate.

  * **Risk management:** While not exactly forecasting, consider providing not just a single forecast but also worst-case and best-case scenarios (perhaps via prediction intervals). For something like BTC, you might even simulate what happens if an extreme event occurred (though beyond typical forecasting – maybe stress test scenarios).

  In summary, **embrace robust methods** (Huber loss, quantile loss) so the model isn’t overly skewed by outliers, and **monitor for anomalies** so that when reality diverges massively from forecast, you catch it and respond (like triggering a model retrain or even a different backup model; e.g., a fallback to a simple model during chaos).

* **Integration with on-chain or sentiment analysis (Bitcoin-specific):** We touched on adding such features in section 5. If certain extreme moves historically were preceded by, say, a major news or unusual on-chain activity (like a big exchange inflow of BTC which might signal sell-off), integrating that info could help predict otherwise surprising moves. However, obtaining and using such data in real-time is a whole project in itself. If available, treat it as exogenous: e.g., a sentiment score that suddenly drops might allow the model to foresee a larger drop than usual.

* **Resilience to manipulation:** Crypto markets occasionally have “wicks” or flash crashes that recover quickly. These appear as extreme out-and-back price moves within an hour. A model might be thrown off by these if they appear as huge volatility. Strategies: possibly smooth the input data slightly (e.g., using a median filter to remove one-tick spikes) or let the model’s loss function handle it. If these wicks are not meaningful to forecast (i.e., you care about closing prices more than intrabar extremes), focus model on those (maybe use hourly close as y, which you likely are). The model likely won’t predict a flash crash wick anyway; it will predict general trend and maybe the intervals will be wide enough to encompass small wicks.

To summarize Bitcoin-specific adjustments:

* **Detect regimes** (trend/volatility) and adapt the modeling strategy (either through features or by switching/weighting models) to improve forecasts under different market conditions.
* **Log-transform prices** to model in relative terms, which helps with non-stationarity and large-scale moves (the model inherently forecasts percentage changes which often are more stable than absolute changes).
* **Include day-of-week/hour features** to handle the 24/7 but weekly cyclic nature of human activity and any possible weekend effects.
* **Use robust training techniques** to not overfit rare extreme moves, and consider them separately as anomalies rather than trying to have the model predict them exactly.
* **Plan for extremes**: have a process to retrain or recalibrate quickly after a big regime change (so the model’s baseline updates to the new normal).
* **Anomaly flags**: possibly output a flag if the model’s prediction interval is far off from actual (post-factum), to signal that an unpredictable event occurred. This can feed into risk management systems (for example, a trading system might reduce position sizes if an anomaly is detected, recognizing model uncertainty is high).

By incorporating these adjustments, your forecasting system becomes more *crypto-aware*. It won’t be easily fooled by a sudden doubling or halving in price – at least it will handle it gracefully by not overshooting subsequent forecasts or by alerting that its confidence is low. And it will account for common patterns like weekend doldrums or Monday volatility if they exist.

## 10. Advanced Topics (Transfer Learning, Online Adaptation, Uncertainty, Anomalies, Multi-step strategies)

Finally, we address some advanced considerations to further enhance and maintain the forecasting solution:

* **Transfer Learning between Crypto Assets:** If you have multiple related time series (e.g., BTC, ETH, LTC prices), you can leverage transfer learning to improve forecasts:

  * **Multi-series training:** As mentioned, NeuralForecast can treat multiple series in one model (global model). You could train a model on a set of cryptocurrencies together. The idea is that the model might learn universal patterns of market behavior (like how volatility clusters, or how technical indicators map to future moves) that apply across assets. It essentially increases the data available to train the model. This is especially useful if some assets have limited history or if you expect similar dynamics (BTC and ETH often move similarly, with ETH having higher beta).

    * Implementation: your DataFrame would contain multiple `unique_id` values (e.g., "BTC", "ETH") and you include any static features (like maybe market cap rank as static exog). The model will have to differentiate series – typically, global models implicitly learn an embedding or at least share parameters across series. Nixtla’s models likely automatically include an ID encoding internally (not positive, but many global models do something like that). If not, you could add `unique_id` as a categorical static feature (like 0 for BTC, 1 for ETH, etc.), and the model will learn different biases or scales for each.
    * The risk is the model might be overwhelmed if series are very different in scale – but you can mitigate via local scaling (NeuralForecast’s `local_scaler_type='robust'` will scale each series by its own stats). That way BTC and ETH are normalized when input to model.
    * After training on multiple series, you can use the model to forecast any of them. If one series (say a smaller altcoin) didn’t have much data, it benefits from others. If you are only interested in BTC forecast but have data on others, training on all can act as data augmentation. This assumes other assets’ behavior provides useful signal for BTC’s pattern learning, which is plausible since crypto markets often move together (though each has idiosyncrasies).
  * **Sequential transfer (pretrain & fine-tune):** As Nixtla’s tutorial describes, you can pretrain a model on one set of series and then fine-tune on another:

    * e.g., Pretrain on a broad dataset (could even be something like all stocks or all crypto) for generic time series features, then fine-tune on BTC. If done properly, this could shorten training time or slightly improve accuracy especially with limited BTC data (120 days is not a lot for a deep model, pretraining might help). They found transfer learning beneficial in some cases (they mention lightning-fast predictions with transfer learning).
    * Implementation: Train a model on large data, save checkpoint. Then initialize a new model of same architecture, load weights, and train further on BTC data (with a lower learning rate to not overwrite everything quickly).
    * Alternatively, use Nixtla’s `TransferLearningDataset` utilities or follow their notebook.
    * Example: Train PatchTST on a large dataset of 1000 stock time series (just as an example of generic pattern learning) for next-day movements. Then load those weights and fine-tune on BTC 1h data. The model might already know to extract seasonal patches, leaving fine-tuning to adjust specifics to BTC’s hourly patterns.
  * **Cross-asset features:** Even without full transfer, you can use related assets’ data as exogenous input. E.g., include Ethereum’s price or returns as a *leading indicator* for BTC (sometimes altcoins move before BTC or vice versa). This makes the model essentially multivariate for prediction, not exactly transfer learning but giving it more context. If ETH is highly correlated, the model could learn to use ETH’s recent trend to inform BTC’s next move. But careful: including other asset prices means for forecasting you need a forecast of those too or assume persistence. It can introduce complexity (like a Vector Autoregression scenario). Possibly easier to just train multi-series global model or separate models and ensemble rather than one model using others as features, unless you have a good reason (some believe flows rotate between BTC and altcoins, etc., which a model could learn).

* **Online Adaptation (partial retraining & fine-tuning on new data):** The crypto market changes quickly, so your model may need frequent updates. Approaches:

  * **Periodic full retraining:** e.g., retrain the model from scratch (or from last weights) every week or month with the latest data. This ensures it learns recent patterns. With Nixtla, you can call `nf.fit()` again on an updated dataset. Lightning by default would start training fresh unless you manage to warm-start (maybe by passing initial weights or continuing training). If you want to *continue training* from where you left off (with new data appended), you could append new data to `Y_df` and call `fit` with fewer epochs. Because the model state is already near optimum for old data, a few epochs on the extended data might suffice to fine-tune. Ensure to shuffle or present data in a way it still remembers older data (Lightning might by default just start the number of steps specified, not treat it as continuing exactly, but if you keep `max_steps` etc., it will just do that number of steps – careful to set it appropriate).
  * **Rolling window training:** Instead of always expanding data, you might drop oldest data beyond a window (to avoid too-long memory if regime changed). This you do manually by slicing your DataFrame before fit.
  * **Stochastic partial fit:** Some algorithms (like online regression, or some incremental learners) allow updating without full retrain. Neural nets typically you would just continue training with new data as above. There’s no built-in “partial\_fit” as in sklearn for a neural net except continuing gradient descent.
  * **Transfer learning approach for adaptation:** You can keep the trained model and do a short fine-tune on just the latest data (for instance, fine-tune on last 1 week’s data for a couple epochs at low LR to specialize on current local regime). This might make it very responsive to recent trend, at risk of forgetting older patterns (catastrophic forgetting). Perhaps freeze some layers and fine-tune just a last layer on recent data. E.g., freeze all but final linear layer of PatchTST, fine-tune bias to recent mean. That might adapt level quickly if price jumped or crashed.
  * **Online learning**: In a streaming scenario, you might update the model each time step. That’s complex for neural nets (you could do one gradient step per new observation, but risk noise and instability). Simpler is batch updates as above.
  * Nixtla’s `DistributedConfig` and MLflow could allow you to automate retraining in production environment and deploy updated model artifact. Perhaps schedule a retraining job nightly using latest data, register new model version if performance improved.
  * **Consider concept drift detectors:** If you want to automatically decide when to retrain, you could monitor if the forecast errors start to increase or if statistical properties (mean, variance of residuals) shift. There are drift detection algorithms (ADWIN, etc.) but simpler: if MAPE over last week is significantly worse than validation MAPE was, trigger a retrain.

* **Forecast Uncertainty Quantification:** Being able to quantify how uncertain the forecast is can be as important as the forecast itself, especially in finance for risk management.

  * Using **prediction intervals from quantile loss or probabilistic models** is recommended. For example, if using NeuralForecast, you can specify `level=[50,80,90]` in losses to get median and 80%, 90% interval forecasts. Models like N-BEATSx, PatchTST, TFT can be trained to output these quantiles by using an appropriate loss (MQLoss). The output will be columns like `model-lo-90`, `model-hi-90` for the 90% interval. In practice, we saw the robust tutorial where they got `Huber-lo-80`, etc..
  * If not using quantile regression, you can estimate uncertainty via **ensemble spread**: e.g., train 5 neural nets with different seeds, get distribution of forecasts. The variance across them gives uncertainty (this captures model uncertainty but not noise uncertainty fully, but often effective).
  * **Bayesian approaches:** There are Bayesian neural network techniques (dropout as approximation, etc.). A simple one: use Monte Carlo dropout – at prediction time, enable dropout and run the model multiple times to sample outputs. Variation in outputs gives an uncertainty estimate. For example, do 100 forward passes with dropout 0.2 active, take empirical quantiles of results. This accounts for model uncertainty if dropout masks simulate different network substructures. It’s easy to implement: `model.train()` (to keep dropout on) and forward multiple times (no backprop).
  * **CRPS and Calibration:** If you produce quantile forecasts, test calibration – e.g., in backtest, check that \~10% of actuals fell below your 10th percentile forecast, etc. If not, adjust. You could apply a simple scaling to forecast variance if intervals are too narrow or wide. Some use methods like quantile regression averaging or normalizing residuals to tune intervals.
  * For multi-horizon distribution, ensure coherence if needed (some advanced methods ensure the whole trajectory distribution, not just marginal quantiles per step – but that’s complex, often assume independence for simplicity).
  * Provide these intervals in your API or reports so that users know the confidence. For example, “BTC price in 24h: median \$50k, 90% interval \[\$45k, \$55k]”. This is valuable for making risk decisions (like how much to hedge).

* **Anomaly Detection Hooks:** We touched on flagging anomalies:

  * Use model residuals as an anomaly detector: If actual price at time t lies outside, say, the model’s 99% prediction interval, that’s an anomaly (could be due to news, etc.). You can raise an alert or trigger special handling (like retraining or switching to fallback strategy).
  * Use a separate model for anomaly detection: e.g., an autoencoder or one-class SVM on sequence of returns could identify outliers that the forecast model doesn’t handle. But simplest is to leverage forecast errors.
  * Nixtla’s TimeGPT product mentions anomaly detection capabilities (historical and online). In practice, you can set up an **anomaly score** = |actual - forecast| / forecast\_sigma (if you have an uncertainty estimate). If this score > some threshold, flag anomaly.
  * Ensure your system has a way to ingest such anomalies – e.g., log them, notify a human or another system. For trading, an anomaly might mean “market regime changed or big info, consider pausing model-based trading”.
  * **Feedback loop:** If an anomaly is detected, you might exclude that point from training or handle it specially. Alternatively, maybe shorten the training window after a regime shock (so new regime data gets more weight).

* **Multi-step forecasting strategies:** We have talked about direct multi-horizon forecasting vs iterative:

  * Nixtla’s models mostly use **Direct Multi-step** (they output all H steps at once, or in blocks). This avoids error accumulation but can be harder to train for long H.
  * If your horizon is quite long (say predicting 7 days ahead hourly = 168 steps), sometimes a combination approach can work:

    * **Direct + Recursive hybrid:** E.g., output 24 hours directly, then for beyond 24 hours, maybe iterate or use a second model. Or output 24h and then use those predictions as features to predict further out (some cascading).
    * **Iterative with exogenous adjusting:** If using iterative (like feeding forecast as input for next step), you could correct bias on the fly. But iterative is tricky for long horizons because small errors compound to big drifts.
    * **MIMO (Multiple Input Multiple Output):** This is essentially direct multi-output (like a model outputting a vector of length H). PatchTST, NHITS, etc. do that. It ensures consistency in one shot.
    * The drawback of direct is needing to decide how to incorporate known future exogenous for each horizon (but Nixtla handles that by providing `futr_exog` for each step if available).
    * **Comparison**: For short horizons, direct is typically best. For very long, sometimes iterative or a specialized long-horizon model (like Autoformer, etc.) can be better. But those specialized models still do effectively direct output with some structure.
    * Ensure your validation covers multi-step performance. For example, maybe your 1-3h ahead error is low but 20+h ahead error is high. If you specifically care about a certain horizon (like exactly 24h), you can train a model just for that horizon (direct single-output for 24h). That’s also a strategy: train separate models for different horizons of interest (say one for next 1h, one for next 24h). This might yield better specialized performance, though you lose the dependency structure. However, for operational use, you might not need every lead time – just specific ones (like tomorrow same time price).
    * Nixtla’s RNN models allow recursive forecast (they note `recursive=True` uses an MLP after RNN encoding to produce direct forecast, else it feeds back). If you want an **autoregressive model** that uses its own outputs as input, you can try setting those RNNs to recursive. But be careful: you should then either unroll during training (teacher forcing vs scheduled sampling issues). Possibly Nixtla’s implementation uses teacher forcing (feeding actual previous outputs during training) and then in predict mode uses its predictions – that can cause discrepancy known as exposure bias. Some advanced solutions (not sure Nixtla has) like using Scheduled Sampling to gradually teach the model to handle its own prediction errors.

  At a practical level, it’s usually easiest to rely on direct multi-step with a well-regularized model (like PatchTST, NHITS are known to handle long horizons by design). Walk-forward validation will tell you if it’s doing okay. If not, consider increasing model capacity or using specialized models (like Autoformer was built for really long horizons by using seasonal decomposition).

* **Modularity & Reproducibility:** (Not explicitly asked, but hinted with “production stability, modularity, reproducibility” in the task.)

  * **Reproducibility:** Set random seeds (Nixtla models have `random_seed` param) to ensure you can reproduce results. Document versions of all packages. Use MLflow to log exactly which data and parameters were used to produce a model. That way, if later you retrain, you can compare metrics apples-to-apples.
  * **Modularity:** Structure your code so that data prep, model training, forecasting, and evaluation are separate components. This makes it easier to replace or upgrade parts (e.g., swap PatchTST with a new model in future). Use config files or dictionaries for hyperparams so you can easily tweak or grid search.
  * **Stability:** Monitor the model’s predictions for any signs of instability (like wildly oscillating predictions). Some models might output spikes if not properly regularized. If so, consider adding smoothing (e.g., output regularization or ensemble with a naive model which bounds extremes).
  * Use try-except around critical operations in production to handle unexpected issues (like if input data is missing or model returns NaN, etc. handle gracefully).

* **MLflow tracking:** If you instrument with MLflow as in Nixtla’s tutorial, you get experiment tracking for free – you can log metrics like validation MAE, the model artifact, and even model lineage. In a production context, you could then use MLflow’s Model Registry to promote a model to production once it’s evaluated. This ties into reproducibility – you can always roll back to a previous model if needed.

* **Integration with MLForecast & StatsForecast in production:** If you plan to use the ensemble, ensure those pieces are also automated. For example, you might retrain ARIMA weekly (since ARIMA might update parameters as data grows). ARIMA is relatively fast for 2880 points, so not an issue. For MLForecast, if you use a LightGBM, retrain it too on updated data. Keep all models synchronized on the same training window for consistency.

* **Edge cases:** Multi-step forecasting of prices could sometimes predict negative prices if model has no constraint (especially if using a differencing or error accumulation). In crypto, price won’t go negative. If your model ever outputs negative (could happen with some linear or RNN extrapolation in weird cases), you should clamp it at 0 in post-processing (or better, use log transform so output is always -∞ at worst which exp to 0, never negative). Log transform inherently prevents negative predictions after back-transform.

* **Multi-target forecasting (jointly volume and price):** As an advanced idea, you could forecast not just price but other related series (like price and volume together). Some models can multi-task. E.g., create a multivariate target that includes both. This can force the model to learn relationships (like usually big volume moves accompany big price moves). But implementing that in Nixtla might require customizing models, which may be beyond scope. Instead, ensemble or use volume as exog.

In essence, the advanced topics revolve around making the forecasting system smarter and robust over time:

* Borrow strength from other data (transfer learning).
* Keep the model updated in a continuously changing market (online adaptation).
* Quantify and communicate uncertainty to users.
* Detect when the model is failing (anomalies) so you don’t blindly trust it.
* Use the appropriate forecasting strategy and maintain modularity for future improvements.

By addressing these, your solution will not just be a static script, but a living system that can adapt to new information, provide insights into its own confidence, and leverage a wider context to improve predictions. This is crucial in the crypto domain where conditions can shift rapidly and unpredictably.

---

**Quick Reference & Best Practices:**

* *Data format:* Always use `DataFrame(unique_id, ds, y)`. For single series, set unique\_id=1 or 'BTC'.
* *Scaling:* Use `scaler_type='robust'` and `revin=True` for NeuralForecast models to handle outliers and shifts.
* *PatchTST:* Start with `patch_len=32, stride=16` (tune around seasonality), hidden\_size \~128, and enable RevIN. Good for long contexts.
* *Alternative models:* Try simpler RNNs (LSTM/GRU) if transformer is too slow; use TFT if interpretability is needed (feature attention).
* *Losses:* Prefer Huber loss or quantile loss for volatility. Evaluate models on MAPE, RMSE, and directional accuracy.
* *Features:* Add technical indicators as hist\_exog and calendar vars as futr\_exog. Ensure alignment and scaling of features.
* *GPU tips:* Use `precision=16` (AMP) for training, adjust `batch_size` to fit memory (32 on 8GB, up to 128 on 16GB if possible). Use gradient accumulation if needed.
* *Ensemble:* Combine neural + ARIMA + ML models for stability. Weight by recent performance or regime. Merge forecasts on `ds` for ensemble calculation.
* *Saving:* Use `nf.save()` and `NeuralForecast.load()` to persist models. Keep track of model versions and hyperparams (MLflow is helpful).
* *FastAPI service:* Load model at startup, expose a `/forecast` endpoint. Precompute future exogenous features before predict. Consider ONNX if deploying on CPU for speed.
* *Monitoring:* Implement Prometheus metrics (request count, latency) and track forecast error vs actual to detect drift. Set up alerts for anomalies (actual out of expected range).
* *Bitcoin-specific:* Use log-price or returns in modeling to stabilize variance. Monitor regime (volatility spikes -> adjust strategies). Use wide prediction intervals during turbulent times and flag anomalies. Retrain frequently as new data comes (at least weekly or when error rises).

By adhering to these practices, you ensure your 1-hour BTC price forecasting pipeline is **comprehensive, resilient, and production-ready** – capable of delivering accurate forecasts along with actionable confidence intervals, and agile enough to adapt to the ever-evolving crypto market.