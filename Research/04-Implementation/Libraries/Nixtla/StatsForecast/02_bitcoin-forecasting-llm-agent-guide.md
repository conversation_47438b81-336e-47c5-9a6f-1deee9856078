---
title: "StatsForecast for Bitcoin Forecasting: LLM Agent Implementation Guide"
permalink: "implementation/libraries/nixtla/statsforecast/bitcoin-forecasting-llm-agent-guide"
type: "technical-guide"
created: "2025-05-24"
last_updated: "2025-05-24"
tags:
  - statsforecast
  - bitcoin
  - cryptocurrency
  - llm-agent
  - implementation
  - statistical-models
  - arima
  - ets
  - garch
  - production
  - code-generation
models:
  - AutoARIMA
  - ARIMA
  - AutoETS
  - ETS
  - GARCH
  - ARCH
  - Theta
  - TBATS
  - MSTL
  - Holt-Winters
techniques:
  - statistical-forecasting
  - volatility-modeling
  - ensemble-methods
  - feature-engineering
  - cross-validation
  - model-selection
  - production-deployment
  - llm-code-generation
libraries:
  - StatsForecast
  - numba
  - pandas
  - numpy
  - statsmodels
  - mlflow
  - fastapi
complexity: "advanced"
datasets:
  - "Bitcoin price data"
  - "Bitcoin OHLCV"
  - "cryptocurrency-exchanges"
  - "on-chain-metrics"
summary: "Comprehensive technical guide specifically designed for LLM agents to generate efficient StatsForecast implementations for Bitcoin forecasting, covering detailed parameter mappings, model configurations, and production deployment patterns"
related:
  - "implementation/libraries/nixtla/statsforecast/bitcoin-forecasting-statsforecast-production-guide"
  - "implementation/libraries/nixtla/neuralforecast/bitcoin-implementation-guide"
  - "domain-applications/cryptocurrency/bitcoin/bitcoin-forecasting-advanced"
  - "case-studies/real-world-applications/bitcoin-forecasting-complete-synthesis"
---

# **Enabling Advanced Bitcoin Forecasting: A Technical Deep Dive into Nixtla's StatsForecast Framework for LLM Agent Script Generation**

## **1\. Introduction to StatsForecast for Production Bitcoin Forecasting**

### **Overview of StatsForecast: Speed, Scalability, and Suitability for Financial Time Series**

StatsForecast is a Python library designed to provide a collection of widely utilized univariate time series forecasting models, with a strong emphasis on optimizing for high performance and scalability.1 It was developed to overcome the limitations in speed and scalability observed in other Python-based statistical modeling alternatives.1 The framework's architecture is particularly well-suited for production environments that demand rapid model training and prediction generation, especially when dealing with the large volumes of data characteristic of financial markets such as Bitcoin.

Key features that underscore its utility include highly optimized implementations of prominent models such as AutoARIMA, AutoETS, AutoCES, MSTL, and Theta.1 Furthermore, StatsForecast offers native compatibility with distributed computing environments like Spark, Dask, and Ray, facilitating the processing of massive datasets.1 The library also incorporates capabilities for probabilistic forecasting, allowing for the generation of prediction intervals, support for exogenous variables to enhance model accuracy, and tools for anomaly detection.1 Comparative performance benchmarks indicate that StatsForecast models can be significantly faster than alternatives like pmdarima, R's standard implementations, Prophet, and statsmodels.1 This performance edge is critical in financial applications where timely insights are paramount. The core design philosophy of StatsForecast, emphasizing computational efficiency and scalability for univariate time series, aligns directly with the demands of processing high-frequency and large-volume data often encountered in Bitcoin markets. This inherent architectural strength makes it a compelling choice for such applications.

### **Relevance to Bitcoin Forecasting: Addressing Volatility and 24/7 Market Dynamics**

The application of StatsForecast to Bitcoin price forecasting is particularly relevant due to the unique characteristics of cryptocurrency markets. Bitcoin prices are notoriously volatile 8, and StatsForecast includes models like GARCH (Generalized Autoregressive Conditional Heteroskedasticity) which are specifically designed to model time series exhibiting non-constant variance over time.1

The 24/7 operational nature of Bitcoin markets 9 presents another challenge that StatsForecast is equipped to handle. This continuous trading activity necessitates models capable of processing ongoing data streams and potentially incorporating calendar-related effects, such as weekend patterns or holiday influences (if observable and significant), as exogenous variables.10 The framework's support for exogenous variables allows for the inclusion of such time-dependent features.

### **Objective: Enabling LLM Agent for Efficient Script Generation**

This report aims to provide a deep technical understanding of the StatsForecast framework to enable a Large Language Model (LLM) coding agent to generate efficient and accurate Python scripts for Bitcoin forecasting. The content is structured to deliver clear, precise, and actionable information regarding model selection, parameterization, data handling, and performance optimization within StatsForecast. An LLM agent requires structured knowledge to effectively utilize a specialized library. Bitcoin forecasting involves complex phenomena like seasonality and volatility. StatsForecast offers specific models (e.g., AutoARIMA for seasonality, GARCH for volatility). This document will clearly delineate which models address particular phenomena and their respective parameters, thereby enabling the LLM to make informed selections and configurations based on high-level forecasting requirements (e.g., "forecast Bitcoin price with weekly seasonality and incorporate volatility clustering").

### **Insights & Implications**

The core design of StatsForecast, centered on computational speed and the ability to scale to numerous time series, makes it a strong candidate for the demanding environment of Bitcoin market analysis. The high-frequency, high-volume nature of Bitcoin data necessitates tools that can process information rapidly and efficiently. StatsForecast's documented speed advantages 1 and its support for distributed computing backends 1 directly address these requirements.

For an LLM coding agent to be effective, it must be able to translate abstract forecasting tasks into concrete StatsForecast implementations. This involves selecting appropriate models (e.g., AutoARIMA for seasonal patterns, GARCH for volatility) and configuring their parameters correctly. This report is structured to provide the necessary detailed mapping between forecasting concepts relevant to Bitcoin (like seasonality, trend, volatility, exogenous factors) and their corresponding implementations and parameterizations within the StatsForecast library.

## **2\. Core Architecture and Performance Optimization of StatsForecast**

### **Framework Internals: Key Components and Data Flow**

StatsForecast operates on a standardized input DataFrame structure, requiring at least three columns: unique\_id (an identifier for each individual time series), ds (a datestamp or an integer time index), and y (the target variable to be forecasted).17 This "long" data format is fundamental to the library's ability to efficiently manage and process multiple time series simultaneously.

The central component of the library is the StatsForecast class. When instantiated, this class accepts a list of model objects (each being an instance of a specific forecasting model from statsforecast.models) and a freq parameter indicating the frequency of the time series data (e.g., 'D' for daily, 'H' for hourly).18

A significant portion of StatsForecast's performance stems from its use of Numba for Just-In-Time (JIT) compilation of critical Python code segments, particularly those involved in model parameter estimation.1 Additionally, some models within the ecosystem are implemented with C++ or Rust backends, which further enhances computational speed by bypassing the Python interpreter for core calculations.26 Wrappers for StatsForecast models, such as those found in AutoGluon, often indicate an init\_time\_in\_seconds \= 0 for these natively compiled models, signifying they do not incur Numba's typical first-run compilation overhead.26

### **Numba JIT Compilation: Mechanism and Performance Gains**

StatsForecast makes extensive use of Numba, a JIT compiler that translates Python functions into optimized machine code at runtime.2 This is particularly applied to computationally intensive parts of the models, such as parameter estimation loops.

The JIT compilation process means that the first time a Numba-decorated function is called with specific argument types, Numba compiles it. This initial compilation step incurs a one-time overhead, which can range from a few seconds (e.g., \~5-10 seconds as noted in documentation examples 18). However, all subsequent calls to the compiled function are significantly faster, often by orders of magnitude (e.g., execution time dropping to \<0.2s after initial compilation 18).

#### **Caching Strategies for Numba**

To mitigate the first-run compilation overhead, especially across different sessions or script executions, Numba provides a caching mechanism.25 StatsForecast can leverage this by setting specific environment variables:

* **Enabling Cache:** Setting the environment variable NIXTLA\_NUMBA\_CACHE=1 instructs Numba to save the compiled machine code to disk.25
* **Cache Location:** By default, Numba stores its cache in \_\_pycache\_\_ directories alongside Python bytecode. This location can be customized by setting the NUMBA\_CACHE\_DIR environment variable to a specific path.25 This allows for a centralized cache that can be persisted.
* **Persistent Caching:** For users who frequently run StatsForecast scripts, adding export NIXTLA\_NUMBA\_CACHE=1 (and optionally export NUMBA\_CACHE\_DIR=/path/to/numba\_cache) to their shell configuration files (e.g., .bashrc, .zshrc) ensures that caching is enabled by default for all sessions.25

An LLM agent generating numerous short-lived scripts or facilitating rapid prototyping for users would significantly benefit from instructing users on or programmatically ensuring persistent Numba caching. Without it, the JIT compilation penalty might be incurred repeatedly, diminishing the perceived performance benefits, especially in development or ad-hoc analysis scenarios. If an LLM is to generate scripts that are efficient from their very first execution in diverse environments, a pre-warmed or persistently available Numba cache is a critical consideration.

### **Parallel Processing**

StatsForecast is architected for scalability through parallel processing, integrating with Fugue to support distributed computation across Dask, Ray, and Spark clusters.1

* **Dask Integration:** To leverage Dask for distributed forecasting, a Dask DataFrame is passed to the StatsForecast.forecast() method. The library then automatically utilizes the existing Dask cluster for computation. It is advisable to test forecasting logic with Pandas DataFrames locally before scaling to Dask. The statsforecast library must be installed on all Dask worker nodes.2
* **Ray Integration:** Integration with Ray allows for the rapid fitting of models across millions of time series. Documentation highlights fitting 1 million series in 30 minutes or 10 benchmark models on 1 million series in under 5 minutes using Ray.1
* **Spark Integration:** Similar to Dask, providing a Spark DataFrame to StatsForecast enables distributed processing over a Spark cluster.

When employing distributed backends like Spark or Dask, the n\_jobs parameter within the StatsForecast object should generally be set to 1\.29 This is because the distributed framework itself manages the parallel execution of tasks across the cluster. Setting n\_jobs \> 1 in this context could lead to nested parallelism, which is typically inefficient and can degrade performance. The LLM agent must be programmed to recognize when a distributed DataFrame is being used and adjust the n\_jobs parameter accordingly. Furthermore, for optimal performance in distributed environments, the input DataFrame should be appropriately partitioned to ensure that work can be evenly distributed among the available executors.4

### **Memory Efficiency**

StatsForecast provides mechanisms to manage memory consumption, which is particularly important when dealing with a large number of time series or long historical data.

* **StatsForecast.forecast() Method:** This method is specifically designed for memory-efficient predictions. It processes time series and generates forecasts without storing the fitted model objects or intermediate partial outputs in memory.20 This approach is analogous to scikit-learn's fit\_predict pattern but optimized for scenarios where the forecast horizon h is known in advance and model introspection is not the primary goal. This makes it highly suitable for production environments where many forecasts are needed quickly.
* **StatsForecast.fit() and StatsForecast.predict() Methods:** In contrast, the standard scikit-learn-style fit() and predict() methods do store the fitted models.20 Calling fit() will train the models and retain them within the StatsForecast object (accessible via the fitted\_ attribute, e.g., sf.fitted\_ 33). Subsequently, predict() uses these stored models to generate forecasts. While this allows for inspection of model parameters and other diagnostics, it consumes more memory.
* **Accessing In-sample Predictions:** If in-sample predictions are required (e.g., for residual analysis or plotting), the StatsForecast.forecast() method can be called with the fitted=True argument. The in-sample fitted values can then be retrieved using the StatsForecast.forecast\_fitted\_values() method.20

The choice between StatsForecast.forecast() and the fit().predict() pattern represents a critical trade-off. For large-scale forecasting tasks where memory is a constraint and detailed inspection of each individual fitted model is unnecessary, forecast() is the preferred method. However, if access to the parameters of the fitted models or detailed diagnostics are required, the fit().predict() sequence is necessary, albeit with higher memory usage. The LLM agent should be capable of selecting the appropriate method based on the user's stated objective, such as prioritizing speed and low memory for batch predictions versus needing model details for analysis.

### **CPU Optimization**

Beyond distributed computing, StatsForecast offers CPU optimization for local machine execution:

* **n\_jobs Parameter:** The StatsForecast class constructor accepts an n\_jobs parameter (e.g., StatsForecast(models=\[...\], freq='D', n\_jobs=-1)).1 This parameter controls the number of parallel processes (using multiprocessing) for fitting models when running on a single machine. Setting n\_jobs=-1 typically utilizes all available CPU cores. Wrappers around StatsForecast models, such as those in the AutoGluon library, also often expose an n\_jobs parameter for similar control.26
* **max\_ts\_length Parameter:** Some model wrappers or higher-level libraries using StatsForecast (e.g., AutoGluon) may offer a max\_ts\_length parameter.26 This parameter, if available and set, can significantly speed up model fitting by using only the most recent max\_ts\_length observations of each time series for training. This truncation can often be performed with minimal or no loss in forecast accuracy, especially if older data points have less relevance to future values.

### **Vectorized Operations (Leveraging Numba)**

Numba's capability to create efficient ufuncs (universal functions) from Python code is a cornerstone of its performance benefits for numerical libraries like StatsForecast. The @vectorize decorator allows a Python function written to operate on scalar inputs to be compiled into a ufunc that can operate element-wise on NumPy arrays with C-like speed.39 Similarly, @guvectorize extends this to functions that operate on arbitrary-sized array sections, enabling more complex vectorized computations.39 These Numba-compiled ufuncs automatically gain NumPy features such as reduction, accumulation, and broadcasting, which are fundamental for efficient time series computations.40

The following table summarizes key performance optimization levers in StatsForecast, which an LLM agent should consider when generating scripts:

**Table 1: StatsForecast Performance Optimization Levers**

| Optimization Area | StatsForecast Mechanism/Parameter | LLM Agent Action/Consideration | Relevant Snippets |
| :---- | :---- | :---- | :---- |
| JIT Compilation | Numba JIT compilation of core functions | Inform user of potential first-run overhead; subsequent runs are faster. | 18 |
| Numba Caching | Env Vars: NIXTLA\_NUMBA\_CACHE=1, NUMBA\_CACHE\_DIR | Recommend setting for persistent caching to avoid re-compilation, especially for frequent or short-lived script executions. Include in environment setup. | 25 |
| Local Parallelism | StatsForecast(..., n\_jobs=-1) | Use n\_jobs=-1 (or appropriate core count) for multi-series fitting on a single machine. | 19 |
| Distributed Parallelism | Dask/Ray/Spark DataFrame input to StatsForecast.forecast() | Set n\_jobs=1 in StatsForecast when using Dask/Ray/Spark. Advise on data partitioning in the distributed DataFrame. | 1 |
| Memory Efficiency | StatsForecast.forecast() vs. fit().predict() | Choose forecast() for large-scale prediction without model inspection. Choose fit().predict() if fitted model parameters/diagnostics are needed. | 20 |
| Input Data Length | max\_ts\_length (in some wrappers like AutoGluon) | If available and appropriate, use to speed up training by considering only recent history. | 26 |

## **3\. Baseline and Core Statistical Models for Bitcoin Forecasting**

### **Overview of Available Models in statsforecast.models**

StatsForecast provides an extensive suite of univariate time series forecasting models, organized into several categories to address diverse forecasting challenges.1 These categories include:

* **Automatic Forecasting:** Models like AutoARIMA, AutoETS, AutoCES, AutoTheta, AutoMFLES, and AutoTBATS automatically search for optimal parameters and select the best model configuration.
* **ARIMA Family:** Includes ARIMA for user-specified orders and AutoRegressive models.
* **Theta Family:** Features various Theta method implementations such as Theta, OptimizedTheta, DynamicTheta, and DynamicOptimizedTheta.
* **Multiple Seasonalities:** Models like MSTL, MFLES, and TBATS are designed for time series with more than one seasonal pattern.
* **GARCH and ARCH Models:** Specifically for modeling time-varying volatility.
* **Baseline Models:** Simple models for establishing benchmark performance.
* **Exponential Smoothing:** A family of models including SimpleExponentialSmoothing, Holt, and HoltWinters.
* **Sparse or Intermittent Models:** Such as ADIDA, CrostonClassic, CrostonOptimized, CrostonSBA, IMAPA, and TSB, suited for series with many zero observations.
* **Machine Learning:** Integration with scikit-learn models via SklearnModel.

Many of these models are capable of producing not only point forecasts but also probabilistic forecasts (prediction intervals) and providing in-sample fitted values for diagnostic purposes.1 Support for exogenous features, which can be critical for Bitcoin forecasting, varies across the different model classes.1 The availability of specialized models, for instance, for intermittent data (like CrostonClassic or IMAPA 17) or complex seasonal patterns, implies that an LLM agent should be equipped to select these more tailored options when data characteristics or user queries suggest their appropriateness, rather than defaulting to general-purpose models like AutoARIMA for all scenarios. For example, if Bitcoin trading data for a specific illiquid pair exhibits sparse non-zero trades, a Croston-type model might be more suitable than AutoARIMA.

### **Baseline Models for Benchmarking**

StatsForecast includes several classical baseline models that are essential for establishing a performance floor against which more sophisticated models can be compared. For Bitcoin forecasting, these include:

* **HistoricAverage**: Forecasts are the mean of all past observations.1
* **Naive**: Forecasts are simply the last observed value.1
* **RandomWalkWithDrift (RWWD)**: Extends the Naive forecast by adding an average drift component, calculated from the historical data.1
* **SeasonalNaive**: Forecasts are the last observed value from the same season in the previous cycle. This model requires a season\_length parameter to define the seasonal period (e.g., 7 for daily data with weekly seasonality).1
* **WindowAverage**: Forecasts are the average of a specified number of past observations (rolling window). Requires a window\_size parameter.1
* **SeasonalWindowAverage**: Similar to WindowAverage but tailored for seasonal data, averaging observations from the same season within a rolling window.1

### **Data Input Format: unique\_id, ds, y**

A consistent data input format is enforced across the StatsForecast library. Models expect a pandas or Polars DataFrame presented in a "long" format, which must contain at least three columns 17:

* unique\_id: A string, integer, or categorical identifier for each distinct time series. This allows multiple series to be processed in a single DataFrame.
* ds: A datestamp (e.g., YYYY-MM-DD or YYYY-MM-DD HH:MM:SS) or an integer representing the time index.
* y: A numeric column containing the target variable values that are to be forecasted.

Any exogenous variables to be included in the models are added as additional columns to this same DataFrame. This standardized input structure greatly simplifies the task for an LLM agent when generating data preparation scripts, as the primary focus will be on transforming user-provided data into this specific long format and ensuring the ds column's frequency aligns with the freq parameter specified in the StatsForecast object.

The following table provides a quick reference for some core model categories and baseline models available in StatsForecast:

**Table 2: Overview of Selected StatsForecast Model Categories and Baselines**

| Model Class/Category | Description | Key Parameters (for LLM) | Probabilistic Support | Exogenous Support | Use Case for Bitcoin |
| :---- | :---- | :---- | :---- | :---- | :---- |
| Naive | Forecasts the last observed value. | None | Yes | No | Simple benchmark for price/return forecasting. |
| SeasonalNaive | Forecasts the last value from the same season. | season\_length | Yes | No | Benchmark for data with clear seasonality (e.g., weekly patterns in daily data). |
| HistoricAverage | Forecasts the mean of all historical data. | None | Yes | No | Simple benchmark, assumes stationarity around a mean. |
| RandomWalkWithDrift | Naive forecast with an average historical drift. | None | Yes | No | Benchmark for trending series. |
| Automatic Forecasters | E.g., AutoARIMA, AutoETS. Automatically select model order/components and parameters. | season\_length, ic (information criterion), etc. | Yes | Varies by model | General purpose forecasting when specific model structure is unknown; good starting point for Bitcoin price/returns. |
| Exponential Smoothing | E.g., HoltWinters, SimpleExponentialSmoothing. Weighted averages of past observations. | season\_length (for seasonal versions), smoothing params | Varies by model | No | Capturing trend and seasonality; AutoETS is often preferred for automatic selection. |

## **4\. Advanced ARIMA Modeling with StatsForecast**

Autoregressive Integrated Moving Average (ARIMA) models are a cornerstone of time series forecasting, and StatsForecast provides robust and optimized implementations.

### **ARIMA and AutoARIMA: Core Functionality**

StatsForecast offers two primary classes for ARIMA modeling:

* **statsforecast.models.ARIMA**: This class allows for the manual specification of the ARIMA model orders, denoted as (p,d,q) for the non-seasonal components and (P,D,Q,s) for the seasonal components, where 's' is the season\_length.41
* **statsforecast.models.AutoARIMA**: This class automates the ARIMA model selection process.1 It searches through a predefined space of (p,d,q)(P,D,Q) combinations and selects the model that optimizes a specified information criterion, typically the Akaike Information Criterion corrected (AICc) by default. This implementation is inspired by and mirrors the functionality of Rob Hyndman's widely respected forecast::auto.arima in R.2

Both ARIMA and AutoARIMA models within StatsForecast support the inclusion of exogenous variables, enabling the construction of ARIMAX-type models.1 This is particularly relevant for Bitcoin forecasting, where external factors can significantly influence price movements.

### **Handling Non-Stationarity: Differencing (d, D parameters)**

A fundamental assumption for ARIMA models is that the time series is stationary. If a series exhibits trends or other forms of non-stationarity in its mean, differencing is applied to stabilize it.

* The d parameter (order of non-seasonal differencing) in ARIMA and AutoARIMA specifies how many times consecutive observations are differenced (yt′​=yt​−yt−1​).6
* The D parameter (order of seasonal differencing) accounts for non-stationarity at the seasonal level (yt′​=yt​−yt−s​).6
* AutoARIMA can automatically determine the appropriate orders of differencing (d and D) using statistical tests. By default, it often employs tests like the Kwiatkowski-Phillips-Schmidt-Shin (KPSS) test (via the test='kpss' parameter) for d, and seasonal strength tests (e.g., seasonal\_test='seas', based on STL decomposition) for D.45

### **Seasonality (SARIMA): season\_length, seasonal\_order, sp parameter**

To model seasonal patterns, Seasonal ARIMA (SARIMA) components are used:

* The season\_length parameter (often denoted as sp in sktime wrappers or m in general ARIMA notation) defines the number of observations in a full seasonal cycle (e.g., season\_length=7 for daily data with weekly patterns, or season\_length=24 for hourly data with daily patterns).6
* The seasonal\_order=(P,D,Q) specifies the orders of the seasonal autoregressive (P), seasonal differencing (D), and seasonal moving average (Q) components. In the ARIMA class, this is set explicitly.43 AutoARIMA automatically searches for the optimal P, D, and Q values.6
* For Bitcoin data, relevant seasonalities might include daily patterns (if using sub-daily data like hourly prices), weekly patterns (e.g., differences in trading behavior between weekdays and weekends), and potentially longer-term cycles like monthly or quarterly, depending on the data granularity and empirical evidence from ACF/PACF plots.63

### **Exogenous Variables (ARIMAX)**

Both ARIMA and AutoARIMA in StatsForecast can incorporate external information through exogenous variables:

* During model fitting (fit method), historical values of exogenous variables are provided via the X argument (typically a NumPy array or part of the input DataFrame).
* For prediction (predict or forecast methods), future values of these exogenous variables must be supplied, usually via an X\_df or X\_future argument.41

#### **Bitcoin-Specific Exogenous Indicators:**

The ability to include exogenous variables is highly valuable for Bitcoin forecasting, as its price can be influenced by a multitude of factors beyond its own past values. An LLM agent should be capable of generating scripts that not only fit ARIMAX models but also prepare these external features.

* **Technical Indicators:** Common trading indicators derived from price/volume data, such as Exponential Moving Averages (EMA), Moving Average Convergence Divergence (MACD), Relative Strength Index (RSI), On-Balance Volume (OBV), and Fibonacci retracement levels, can be engineered and included.64 The LLM would need to generate Python code (e.g., using libraries like pandas for rolling calculations or specialized libraries like TA-Lib) to compute these indicators and merge them with the primary Bitcoin price series.
* **Market Microstructure Features:** More advanced indicators like VPIN (Volume-Synchronized Probability of Informed Trading) or the Roll measure, which capture aspects of market liquidity and information asymmetry, can also serve as potent exogenous predictors.67
* **Other External Factors:** Prices of other cryptocurrencies (potential lead-lag effects or correlations), overall stock market indices, trading volumes, social media sentiment scores, and blockchain-specific metrics (e.g., transaction counts, hash rate) are also candidates for exogenous variables.10
* **Handling Calendar Effects (Weekends, Holidays):** Given Bitcoin's continuous 24/7 trading, traditional market holiday effects might be less pronounced, but "weekend effects" or other calendar-driven patterns might still exist. These can be modeled by creating dummy (binary) variables for specific days of the week or notable periods and including them as exogenous regressors.10 Nixtla's ecosystem offers utilities like CountryHolidays (demonstrated with TimeGPT but applicable in principle) or the date\_features parameter for automatic generation of such calendar features.15
* **Fourier Terms for Complex Seasonality (Dynamic Harmonic Regression):** For modeling complex or multiple seasonal patterns (e.g., intra-day and intra-week patterns in hourly Bitcoin data), Fourier series can be employed. Sine and cosine terms are generated for each relevant seasonal period using functions like utilsforecast.feature\_engineering.fourier.71 These generated Fourier terms are then treated as exogenous variables in the AutoARIMA model. A critical configuration detail for this approach is to set seasonal=False (and potentially max\_d=0 if differencing is also handled by regressors) within AutoARIMA.62 This ensures that the model relies on the explicit Fourier terms for capturing seasonality rather than its own internal SARIMA components, effectively mimicking a dynamic harmonic regression. For instance, one might define features \= \[partial(fourier, season\_length=24, k=10), partial(fourier, season\_length=24\*7, k=5)\] for hourly data with daily and weekly patterns, then use utilsforecast.pipeline to add these to the DataFrame before fitting AutoARIMA(max\_d=0, seasonal=False).

### **Information Criteria Tuning**

AutoARIMA employs information criteria to select the optimal model from the candidates it evaluates.

* The ic parameter in AutoARIMA allows specification of the criterion: 'aicc' (Akaike Information Criterion corrected \- default), 'aic' (Akaike Information Criterion), or 'bic' (Bayesian Information Criterion).6
* AICc is generally preferred for smaller sample sizes, while BIC tends to select more parsimonious models (fewer parameters). The LLM should be able to select an appropriate criterion based on user preference or data characteristics.
* The AIC, BIC, and AICc values of the final selected ARIMA model can be accessed from the fitted model object, typically stored within the model\_ attribute, e.g., fitted\_model.model\_\['aic'\], fitted\_model.model\_\['bic'\].72

### **Custom Search Bounds for AutoARIMA**

The "automatic" nature of AutoARIMA can be significantly guided by specifying bounds for the search space of ARIMA orders. This allows users to trade off between the thoroughness of the search and computational time.

* Parameters such as start\_p, start\_q, start\_P, start\_Q define the initial orders for the stepwise search procedure.
* max\_p, max\_q, max\_P, max\_Q set the upper limits for these orders.
* max\_d and max\_D control the maximum degrees of non-seasonal and seasonal differencing, respectively.
* max\_order can limit the total sum of orders (p+q+P+Q) if a non-stepwise search is performed.6
* The stepwise=True parameter (default) enables a faster, heuristic search. Setting stepwise=False conducts a more exhaustive search over all models within the defined constraints but can be very slow, especially for seasonal models.6 If stepwise=False, setting parallel=True can leverage multiple cores to speed up this extensive search.45 The LLM agent should be able to interpret user requests for different search strategies (e.g., "quick ARIMA fit" vs. "thorough ARIMA model search") and map them to the appropriate AutoARIMA parameters.

### **Refitting Strategies**

In production environments where new data arrives sequentially, models often need to be updated.

* The core statsforecast.models.ARIMA and statsforecast.models.AutoARIMA classes do not feature an explicit update method for incremental parameter updates based on new data points.41
* Refitting typically involves:
  1. **Full Retraining:** Calling the fit() method again with an extended dataset that includes the new observations. This re-estimates all model parameters from scratch.
  2. **Using forward():** The forward(y, h,...) method allows applying an *already fitted* model (with its existing parameters) to a new segment of time series data y to produce forecasts for h steps ahead.41 This is not a refit in the sense of updating model parameters based on y; rather, it's a way to generate predictions using the old parameters on new data. This can be useful for quick, rolling predictions if the underlying data generating process is assumed to be stable.
* For systematic refitting and evaluation, the StatsForecast.cross\_validation() method is highly relevant. It allows specifying refitting strategies (e.g., refit at every window, or every n windows) through its refit parameter.19 This method simulates how a model would perform in production with periodic retraining. The LLM should correctly select between a full fit() or using forward() based on whether the user intends to update model parameters or simply obtain new predictions from a static, previously trained model.

### **Structural Break/Change Point Handling**

ARIMA models assume parameter constancy over the fitting period. Significant structural breaks in the time series (e.g., due to major market events, regulatory changes affecting Bitcoin) can violate this assumption and lead to poor forecast performance.74

* StatsForecast's ARIMA and AutoARIMA models do not appear to have built-in mechanisms for automatically detecting or adapting to structural breaks.23
* Addressing structural breaks typically requires external strategies:
  1. **External Detection:** Utilize specialized libraries like ruptures in Python for change point detection 76, or implement statistical tests such as the Chow test or Bai-Perron test if available and applicable.74
  2. **Data Segmentation:** If break points are identified or known, the time series can be split into segments, and separate ARIMA models can be fitted to each distinct regime.74
  3. **Exogenous Dummies:** Introduce dummy variables as exogenous regressors to flag the periods before and after known breaks.
  4. **Frequent Refitting:** Employing a rolling window approach with frequent refitting (e.g., via StatsForecast.cross\_validation or a custom loop) can help the model adapt more quickly to new data regimes, though it doesn't explicitly model the break.
  5. The approximation parameter in AutoARIMA, when used with truncate, allows model selection to be based on the most recent truncate observations.6 While not a direct structural break handling mechanism, it can make the model more responsive to recent changes if a break has occurred recently. The LLM agent, if tasked with creating solutions robust to structural breaks, would need to generate code that implements these external detection or segmentation strategies, or carefully configure frequent refitting, in conjunction with StatsForecast model calls.

### **Fractional Differencing (ARFIMA)**

ARFIMA (Autoregressive Fractionally Integrated Moving Average) models are an extension of ARIMA that allow the differencing parameter d to take non-integer (fractional) values. This makes them suitable for modeling long-memory processes, where autocorrelations decay more slowly than exponentially.44

* The reviewed documentation for StatsForecast's core ARIMA and AutoARIMA classes does not indicate support for fractional differencing (i.e., an ARFIMA implementation).1 If ARFIMA modeling is required for Bitcoin data (e.g., if long memory characteristics are identified), users would likely need to turn to other specialized Python libraries (like arfima in R, or potentially Python ports if available) or implement fractional differencing as a preprocessing step before using StatsForecast's ARIMA.

The following table details key AutoARIMA parameters relevant for LLM configuration when forecasting Bitcoin prices:

**Table 3: Key AutoARIMA Parameters for LLM Configuration in Bitcoin Forecasting**

| Parameter Name | Description | Typical Values/Settings for Bitcoin | LLM Agent Considerations | Relevant Snippets |
| :---- | :---- | :---- | :---- | :---- |
| season\_length | Length of the seasonal cycle. | 7 (daily data, weekly seasonality), 24 (hourly data, daily seasonality), 30 (daily data, monthly approx.), etc. Depends on data frequency and observed patterns. | Prompt user for data frequency and suspected seasonal periods. Default to 1 if unsure or non-seasonal. | 41 |
| d | Order of non-seasonal differencing. | None (for auto-detection), 0, 1, or 2\. For Bitcoin prices (often non-stationary), d=1 (for returns) or d=2 is common. None lets AutoARIMA decide. | Allow user to specify, or use None for automation. If None, ensure test parameter is appropriate. | 41 |
| D | Order of seasonal differencing. | None (for auto-detection), 0, or 1\. | Allow user to specify, or use None for automation. If None, ensure seasonal\_test is appropriate. | 41 |
| max\_p, max\_q, max\_P, max\_Q | Maximum orders for AR, MA, SAR, SMA components. | Defaults (e.g., 5 for p/q, 2 for P/Q) are often reasonable starting points. Can be increased for more complex dynamics or longer seasonalities, at the cost of computation. | Provide defaults. Allow user to override for finer control over search space. | 41 |
| ic | Information criterion for model selection. | 'aicc' (default), 'aic', 'bic'. 'aicc' for smaller samples, 'bic' for parsimony. | Default to 'aicc'. Allow user to select. | 41 |
| stepwise | Whether to use stepwise algorithm for model selection. | True (default, faster), False (more exhaustive, slower). | Default to True. Offer False (with parallel=True) for more thorough search if user requests. | 45 |
| seasonal | Whether to consider seasonal models. | True if seasonality is suspected in Bitcoin data (e.g., weekly patterns). False if using Fourier terms as exogenous variables. | Default to True. Set to False if dynamic harmonic regression pattern is intended. | 41 |
| X (in fit), X\_df (in predict/forecast) | Exogenous variables. | DataFrame/NumPy array of technical indicators, sentiment scores, holiday dummies, Fourier terms. | Prompt user if they have exogenous features. Explain format requirements (historical and future values). | 41 |
| approximation | Use approximation for fitting. | True (default) for faster estimation, especially for long series or high season\_length. False for exact MLE throughout. | Default to True. Offer False if higher precision is critical and computation time is not a constraint. | 45 |
| allowdrift, allowmean | Whether to consider models with drift or a non-zero mean. | True (default for allowdrift in some contexts, allowmean default is True in AutoGluon wrapper). Bitcoin prices often have trends (drift). | Default to True for both, as Bitcoin series are rarely zero-mean stationary without differencing. | 28 |

## **5\. Exponential Smoothing Techniques (ETS) for Bitcoin**

Exponential Smoothing (ETS) models provide a flexible framework for forecasting time series by modeling error, trend, and seasonal components.

### **ETS and AutoETS: Overview and Parameters**

StatsForecast includes statsforecast.models.AutoETS, which automatically selects the best-fitting ETS model from a family of 30 potential candidates.1 This selection is typically based on minimizing an information criterion, such as the Akaike Information Criterion corrected (AICc), by default. The implementation mirrors Hyndman's forecast::ets package in R.

Key parameters for AutoETS include:

* season\_length: Integer specifying the number of observations per seasonal cycle (e.g., 7 for daily data with weekly seasonality).22
* model: A three-character string (e.g., "ANN", "MAM", "ZZZ") defining the Error (E), Trend (T), and Seasonality (S) components.22 Each character can be 'A' (Additive), 'M' (Multiplicative), 'N' (None), or 'Z' (Automatically Optimized). The default "ZZZ" allows AutoETS to select the best type for each component.
* damped: A boolean parameter indicating whether to apply a damped trend. A damped trend converges to a constant level in the long run, which can be more realistic for many series, including volatile ones like Bitcoin.22
* phi: The damping parameter (between 0 and 1\) used if damped=True.86 If None, it's estimated.

The AutoETS model simplifies the selection process within this rich family of models. An LLM agent should leverage AutoETS, particularly with the model="ZZZ" default, when a user requests exponential smoothing but is uncertain about the specific error, trend, or seasonality characteristics of the Bitcoin data.

### **Error Types (Additive/Multiplicative), Trend (Damped), Seasonality**

The model string in AutoETS is crucial for defining the model structure:

* **Error (E):** 'A' for additive errors (yt​=level+ϵt​) or 'M' for multiplicative errors (yt​=level×(1+ϵt​)). Multiplicative errors are often more appropriate for financial time series like Bitcoin where the variance tends to increase with the price level.82 When errors are multiplicative, point forecasts typically represent the median of the forecast distribution rather than the mean.91
* **Trend (T):** 'N' for no trend, 'A' for additive trend (bt+1​=bt​+slope), or 'M' for multiplicative trend (bt+1​=bt​×factor). The trend can also be damped ('Ad' or 'Md') by setting damped=True, causing the trend to flatten out over longer forecast horizons.26 This damping can be beneficial for Bitcoin price series to prevent overly optimistic or pessimistic long-term trend extrapolations.
* **Seasonality (S):** 'N' for no seasonality, 'A' for additive seasonality (yt​=level+st​), or 'M' for multiplicative seasonality (yt​=level×st​).

### **State-Space Model Representation**

ETS models are formulated within a state-space framework.26 This involves a state vector (vt​) that captures the unobserved components like level, trend, and seasonality. The state vector evolves over time according to a transition equation (vt​=f(vt−1​)+g(vt−1​)ϵt​), and the observed time series values (yt​) are generated from the state via a measurement equation (yt​=w(vt−1​)+r(vt−1​)ϵt​).93 This representation allows for optimal estimation of parameters and generation of prediction intervals.

### **Handling Multiple Seasonalities**

Standard ETS models are designed to handle a single seasonal pattern defined by season\_length. For time series with multiple distinct seasonalities (e.g., hourly Bitcoin data exhibiting both daily and weekly patterns), alternative approaches are generally recommended:

* **MSTL (Multiple Seasonal-Trend decomposition using LOESS):** This model can decompose the series into multiple seasonal components and a trend. The trend component can then be forecasted using an ETS model (by setting trend\_forecaster=AutoETS()).19
* **Dynamic Harmonic Regression:** Use Fourier terms as exogenous variables with an ARIMA model (as discussed in the ARIMA section) or potentially with an ETS model if it supports exogenous regressors (though AutoETS in StatsForecast does not explicitly list exogenous variable support in the provided snippets 1).

### **Outlier Robustness and Preprocessing**

The documentation for statsforecast.models.AutoETS 41 does not specify parameters or built-in mechanisms for handling outliers. However, the inherent smoothing nature of ETS models may provide some degree of robustness to minor, isolated outliers.62 For instance, models like MSTL (which can use ETS for trend forecasting) employ LOESS smoothing, which can reduce the impact of noise and outliers.96 The CrostonOptimized model is also explicitly mentioned as being robust to outliers.97

For Bitcoin data, which can experience significant, sudden price jumps (flash crashes or pumps), relying solely on the model's inherent smoothing might be insufficient. Standard preprocessing best practices should be considered:

1. **Detection:** Identify outliers using statistical methods (e.g., Z-scores, Interquartile Range (IQR) based thresholds) or visual inspection.98
2. **Treatment:** Depending on the nature of the outlier (error vs. genuine extreme event), options include removal, winsorization (capping extreme values at a certain percentile), or replacement with an imputed value (e.g., median, interpolated value).62 An LLM agent should be programmed to suggest or implement these preprocessing steps if the context of the Bitcoin data suggests a high likelihood of influential outliers.

### **Prediction Interval Calibration: prediction\_intervals parameter, Conformal Prediction (ConformalIntervals, n\_windows, h)**

AutoETS models in StatsForecast support probabilistic forecasting, enabling the generation of prediction intervals.1

* The level parameter in StatsForecast.forecast() or StatsForecast.cross\_validation() methods can be used to specify the desired confidence levels for these intervals (e.g., level=).20
* For more robust and potentially better-calibrated prediction intervals, especially for volatile and non-normally distributed series like Bitcoin, StatsForecast offers **Conformal Prediction**. This is achieved by passing a ConformalIntervals object to the prediction\_intervals parameter, either during model instantiation (e.g., AutoETS(prediction\_intervals=ConformalIntervals(...))) or when calling StatsForecast.forecast() or StatsForecast.cross\_validation().20
* The ConformalIntervals class takes parameters such as:
  * h (int): The forecast horizon used during the internal cross-validation process to compute conformity scores.
  * n\_windows (int): The number of cross-validation windows used to generate the conformity scores. It's recommended that n\_windows \* h should be less than the length of the time series, and n\_windows should be at least 2\.102 Conformal prediction is a distribution-free method, meaning it does not rely on assumptions about the underlying distribution of forecast errors (e.g., normality), which is a significant advantage for Bitcoin data.20 The LLM should prioritize recommending conformal prediction when robust uncertainty quantification is required for Bitcoin forecasts.

The following table outlines key AutoETS parameters and their relevance for LLM configuration in Bitcoin forecasting:

**Table 4: AutoETS Configuration for Bitcoin Price Forecasting**

| Parameter Name | Description | Relevance to Bitcoin | LLM Agent Action | Relevant Snippets |
| :---- | :---- | :---- | :---- | :---- |
| season\_length | Number of observations per seasonal cycle. | Crucial for capturing weekly (7 for daily data) or other periodicities in Bitcoin prices. | Prompt user for data frequency and known seasonalities. | 38 |
| model | Three-character string (ET,S) for Error, Trend, Seasonality type (A, M, N, Z). | "M" (Multiplicative) for Error and/or Seasonality often suitable for price series where variance scales with level. "Z" allows automatic selection. | Default to "ZZZ" for full automation. Suggest "M" components if multiplicative effects are suspected or observed. | 82 |
| damped | Boolean, whether to use a damped trend. | True can be beneficial for volatile Bitcoin series to prevent unrealistic long-term trend extrapolations. | Consider suggesting True or making it an optimizable hyperparameter if long-term forecasts are needed. | 83 |
| phi | Damping parameter (0-1) if damped=True. | Controls the rate of trend damping. None for automatic estimation. | Typically leave as None for AutoETS to estimate. | 86 |
| prediction\_intervals | Configuration for prediction intervals. | Use ConformalIntervals(h=..., n\_windows=...) for robust, distribution-free intervals for volatile Bitcoin data. | Recommend and implement ConformalIntervals when probabilistic forecasts are requested. Guide user on setting h and n\_windows. | 20 |

## **6\. Volatility Modeling with GARCH in StatsForecast**

Generalized Autoregressive Conditional Heteroskedasticity (GARCH) models are specifically designed to capture time-varying volatility, a hallmark of financial time series like Bitcoin.

### **GARCH and ARCH Models: Parameters (p, q) and Use Cases for Bitcoin Volatility**

StatsForecast provides implementations of statsforecast.models.GARCH(p, q) and statsforecast.models.ARCH(p).1

* In a GARCH(p,q) model, p represents the order of the ARCH terms (dependency on p lagged squared residuals, ϵt−i2​) and q represents the order of the GARCH terms (dependency on q lagged conditional variances, σt−j2​).13
* The ARCH(p) model is a special case of GARCH(p,0), where the conditional variance depends only on past squared residuals.
* These models are critical for Bitcoin forecasting due to the phenomenon of **volatility clustering**, where periods of high volatility tend to be followed by more high volatility, and calm periods by calm.14
* GARCH models are typically applied to the log returns of asset prices (e.g., rt​=log(Pt​/Pt−1​)), as returns often exhibit more stationary behavior in their mean, while their variance is time-dependent.13
* For model stability and meaningful interpretation, GARCH parameters must satisfy certain constraints: the constant term ω\>0, ARCH coefficients αi​≥0, GARCH coefficients βj​≥0. For stationarity of the variance process, the sum of αk​+βk​ (over relevant lags) must be less than 1\.13

### **Available GARCH Variants (EGARCH, GJR-GARCH, GARCH-M)**

The core documentation and tutorials for StatsForecast primarily feature the standard GARCH and ARCH models.1 There is no explicit mention in the reviewed snippets of built-in support within statsforecast.models for common GARCH extensions like:

* **EGARCH (Exponential GARCH):** Captures asymmetric responses of volatility to positive and negative shocks and models the logarithm of the conditional variance, thus not requiring non-negativity constraints on parameters.111
* **GJR-GARCH (Glosten-Jagannathan-Runkle GARCH):** Incorporates a term to model the leverage effect, where negative shocks typically have a larger impact on future volatility than positive shocks of the same magnitude.111
* **GARCH-M (GARCH-in-Mean):** Allows the conditional variance to directly influence the conditional mean of the time series.112

For Bitcoin, which, like other financial assets, often exhibits leverage effects, these advanced GARCH variants can be crucial for more accurate volatility modeling. If these specific variants are required, users would likely need to employ other specialized Python libraries such as arch.111 The LLM agent should be aware of this limitation and, if a user requests a model for asymmetric volatility, guide them towards appropriate external libraries or clarify that StatsForecast's GARCH is the standard symmetric version.

### **Comparison with arch library**

The arch Python library is a dedicated package for estimating and forecasting a wide variety of ARCH-type models, including EGARCH, GJR-GARCH, and many others.106

* **StatsForecast GARCH:** Offers an implementation integrated within its broader forecasting framework, benefiting from Numba optimizations for speed and a unified API for handling multiple series. Its focus is on providing a fast GARCH model within this ecosystem.
* **arch library:** Provides a more extensive suite of econometric volatility models and diagnostic tools tailored for detailed financial econometrics. As noted in 186, standard GARCH models might be considered overparameterized by some and do not inherently capture leverage effects, which is an argument for using extensions often found in the arch library.

### **Calculating VaR and CVaR**

Value at Risk (VaR) and Conditional Value at Risk (CVaR, also known as Expected Shortfall) are standard risk management metrics.

* The StatsForecast GARCH documentation suggests its models can be used to estimate VaR and CVaR.14 However, specific built-in functions for direct calculation are not detailed in the provided snippets.14
* The typical procedure to calculate these risk measures using GARCH model outputs involves:
  1. **Forecast Conditional Volatility (σt+h​):** Obtain the h-step ahead forecast of the conditional standard deviation from the fitted GARCH model.
  2. **Forecast Conditional Mean (μt+h​):** Obtain the h-step ahead forecast of the conditional mean from an appropriate mean model (e.g., ARIMA, or a constant if returns are assumed to have a zero mean).
  3. **Assume Residual Distribution:** Assume a distribution for the standardized residuals (zt​=ϵt​/σt​), e.g., Normal, Student's t-distribution.
  4. **Calculate VaR:** For a given confidence level α, VaR is calculated as VaRα​=μt+h​+σt+h​×Qα​(Z), where Qα​(Z) is the α-quantile of the assumed standardized residual distribution.122
  5. **Calculate CVaR:** CVaR at level α is the expected loss given that the loss exceeds VaRα​.122 Its calculation depends on the assumed distribution. The LLM agent would need to generate Python code to perform these calculations using the volatility forecasts from StatsForecast's GARCH model and statistical functions from libraries like scipy.stats.

### **Volatility Regime Detection**

GARCH models, by their nature, capture periods of changing volatility (volatility clustering).14 Persistently high or low forecasted conditional variances from a GARCH model can be indicative of different volatility regimes.
However, GARCH models do not explicitly identify discrete regime states. For formal regime detection and modeling (e.g., switching between a low-volatility and a high-volatility state), specialized models like Markov Switching GARCH (MS-GARCH) are typically used. These are not mentioned as being available in StatsForecast in the provided materials. An LLM agent could generate scripts to plot the forecasted conditional volatility from StatsForecast's GARCH model, allowing users to visually inspect for potential regime shifts.
Before applying GARCH models, it's crucial to analyze the residuals of a fitted mean model (e.g., ARIMA applied to log returns). If these residuals exhibit ARCH effects (autocorrelation in their squares), then a GARCH model is appropriate for modeling this remaining conditional heteroskedasticity. Tests like the Ljung-Box test on squared residuals or the ARCH-LM test can be used for this purpose.14 The LLM should be capable of generating scripts for this two-step process: first fitting a model for the conditional mean, then diagnosing its residuals, and finally fitting a GARCH model to the residuals if ARCH effects are present.

The following table details parameters for the standard GARCH model in StatsForecast:

**Table 5: GARCH Model Configuration in StatsForecast for Bitcoin Volatility**

| Parameter (GARCH) | Description | Relevance to Bitcoin Volatility | LLM Agent Action | Relevant Snippets |
| :---- | :---- | :---- | :---- | :---- |
| p | Order of the ARCH terms (lagged squared residuals). | Determines how much past shocks (squared residuals) influence current volatility. Common choice for financial data is 1\. | Default to 1 or allow user specification. Suggest diagnostics (ACF/PACF of squared residuals) to guide choice. | 13 |
| q | Order of the GARCH terms (lagged conditional variances). | Determines how much past conditional variances influence current volatility. Common choice for financial data is 1 (for GARCH(1,1)). | Default to 1 (for GARCH(p,1)) or 0 (for ARCH(p)). Allow user specification. | 13 |

## **7\. Other Relevant StatsForecast Models for Bitcoin**

Beyond ARIMA, ETS, and GARCH, StatsForecast offers several other models that could be relevant for specific aspects of Bitcoin forecasting. The availability of these specialized models means an LLM agent should be capable of more nuanced model selection, moving beyond general-purpose defaults when user queries or data characteristics suggest these alternatives.

### **MSTL: Handling Multiple Seasonalities**

The Multiple Seasonal-Trend decomposition using LOESS (MSTL) model is designed for time series exhibiting multiple distinct seasonal patterns, such as daily and weekly cycles in hourly Bitcoin data.1

* **Parameters:**
  * season\_length: A list of integers specifying the lengths of each seasonal period (e.g., \[24, 24\*7\] for hourly data with daily and weekly seasonality).19
  * trend\_forecaster: An instantiated StatsForecast model (e.g., AutoARIMA(), AutoETS()) or an SklearnModel (e.g., SklearnModel(LinearRegression())) used to forecast the trend component after decomposition.19 Seasonal components are typically forecasted using SeasonalNaive.
* **Suitability for Bitcoin:** If Bitcoin price data (especially at higher frequencies like hourly) clearly shows multiple, superimposed seasonal patterns (e.g., intra-day patterns combined with day-of-week effects), MSTL offers a robust way to decompose and forecast these.

### **Theta Models (Theta, AutoTheta, DynamicOptimizedTheta, DynamicStandardTheta): Suitability for Trend and Volatility**

The Theta family of models works by decomposing the time series into two "theta lines," effectively separating long-term trend information from short-term fluctuations and then combining their forecasts.1

* AutoTheta: Automatically selects the best model among Standard Theta Model (STM), Optimized Theta Model (OTM), Dynamic Standard Theta Model (DSTM), and Dynamic Optimized Theta Model (DOTM) based on Mean Squared Error (MSE).41
* **Parameters:** Typically include season\_length and decomposition\_type (often "additive" or "multiplicative").31
* DynamicOptimizedTheta (DOTM) and DynamicStandardTheta (DSTM) are designed to continuously update their internal components as new data becomes available, making them well-suited for non-stationary series where the underlying data structure evolves over time.31
* **Suitability for Bitcoin:** Theta models, particularly the dynamic and optimized versions, can be effective for capturing the strong, often non-linear trends observed in Bitcoin prices. Their decomposition approach might offer some robustness to noise. They could be strong candidates for the trend\_forecaster component in an MSTL model or as part of an ensemble, given Bitcoin's evolving trend behavior.

### **Croston Methods (CrostonClassic, CrostonOptimized, CrostonSBA): For Intermittent Trading Activity**

The Croston family of models is specifically designed for forecasting intermittent time series, which are characterized by many periods of zero demand (or activity) interspersed with sporadic non-zero values.1

* CrostonOptimized is noted for its robustness to outliers.97
* **Applicability to Bitcoin:** Generally, Bitcoin trading is continuous, and its price series do not exhibit the kind of zero-value intermittency these models are designed for. However, in niche scenarios—such as forecasting trading volume for extremely low-liquidity altcoin pairs, or analyzing transaction counts on a very inactive blockchain where periods of no activity are common—these methods *might* find application. The LLM agent should exercise caution and recommend these only if the Bitcoin-related data truly fits the intermittent profile.

### **TBATS/AutoTBATS: Complex Seasonalities**

TBATS (Trigonometric, Box-Cox, ARMA errors, Trend, and Seasonal components) models are powerful for handling time series with complex seasonal patterns, including multiple seasonalities, non-integer seasonal periods, and non-nested seasonalities.1

* AutoTBATS: Automatically selects key model components like the use of Box-Cox transformation, trend, damped trend, and ARMA errors, typically based on AIC.41
* **Parameters:** Requires seasonal\_periods (a list of seasonal lengths) and allows specification of options like use\_boxcox, use\_trend, use\_damped\_trend, etc..41
* **Suitability for Bitcoin:** If Bitcoin data exhibits highly complex, overlapping seasonal patterns that are not well captured by MSTL or Fourier terms with ARIMA, TBATS could be a more sophisticated alternative. However, these models can be computationally more intensive.

The following table summarizes these specialized models for potential Bitcoin forecasting scenarios:

**Table 6: Specialized StatsForecast Models for Bitcoin Forecasting Scenarios**

| Model Class | Key Parameters | Bitcoin Use Case | Strengths for Bitcoin | LLM Considerations |
| :---- | :---- | :---- | :---- | :---- |
| MSTL | season\_length (list), trend\_forecaster | Hourly/sub-daily Bitcoin data with clear daily, weekly, and/or annual patterns. | Explicitly models multiple additive/multiplicative seasonalities. Flexible trend modeling. | Guide user on identifying multiple season\_length values. Suggest appropriate trend\_forecaster (e.g., AutoETS). |
| AutoTheta / DynamicOptimizedTheta | season\_length, decomposition\_type, model (for AutoTheta) | Capturing overall price trends, potentially robust to noise. Useful for non-stationary series. | Adapts to changing trends (dynamic versions). Simple yet effective for certain trend structures. | Suggest when trend is dominant. AutoTheta simplifies selection. Dynamic versions for evolving trends. |
| CrostonOptimized | alpha (smoothing, often auto-selected) | Very niche: e.g., trading volume of extremely illiquid, sporadically traded crypto assets. | Robust to outliers, designed for zero-inflated series. | Use with extreme caution for Bitcoin price. Confirm data truly exhibits intermittency. |
| AutoTBATS | seasonal\_periods (list), use\_boxcox, use\_trend, etc. | Bitcoin data with very complex, non-integer, or multiple interacting seasonalities. | Handles highly complex seasonal patterns, Box-Cox transformation. | Computationally more intensive. Use if simpler methods (MSTL, Fourier ARIMAX) fail to capture seasonality. |

## **8\. Ensemble Integration and Advanced Forecasting Strategies**

While StatsForecast provides a rich set of individual forecasting models, advanced forecasting often benefits from combining the strengths of multiple models or employing more sophisticated strategies.

### **Model Averaging/Combination Techniques (External to StatsForecast)**

StatsForecast primarily focuses on efficiently fitting individual univariate models. The task of combining forecasts from multiple such fitted models (model averaging or ensembling) is generally performed externally to the core library functions.1

* **Simple Averaging:** The most straightforward approach is to average the point forecasts produced by several different models (e.g., an ARIMA, an ETS, and a Theta model).
* **Weighted Averaging:** Forecasts can be combined using weights, which could be assigned based on historical performance (e.g., inversely proportional to their Mean Squared Error (MSE) on a validation set or cross-validation folds).137
* **Stacking/Regression-Based Combination:** More advanced methods involve training a meta-model (e.g., a linear regression, gradient boosting machine) to learn the optimal combination of base model forecasts. The predictions from the base StatsForecast models serve as input features to this meta-model.137 An LLM agent could generate Python scripts that first obtain forecasts from multiple StatsForecast models and then implement these combination techniques using libraries like NumPy for simple/weighted averaging or scikit-learn for stacking regressors. Given Bitcoin's inherent complexity and the difficulty in finding a single "best" model, ensembling diverse StatsForecast models is a practical strategy to enhance forecast robustness and potentially improve accuracy.

### **Dynamic Model Switching/Regime-Based Forecasting (Conceptual, external implementation)**

Financial time series, including Bitcoin prices, often exhibit regime-switching behavior (e.g., periods of low volatility followed by periods of high volatility, or bull vs. bear market trends). StatsForecast does not appear to have built-in functionalities for automatically detecting these regimes and dynamically switching between different forecasting models based on the current regime.32

* **Conceptual Frameworks:** Methodologies for regime-switching include Threshold Autoregressive (TAR) models, Hidden Markov Models (HMMs), and Smooth Transition Autoregressive (STAR) models.138 These frameworks allow model parameters or even entire model structures to change depending on an observed or latent state variable.
* **External Implementation:** To implement such a strategy with StatsForecast, one would typically:
  1. **Regime Detection/Classification:** Use external methods to identify or predict the current market regime. This could involve statistical tests, GARCH model outputs (e.g., classifying volatility as high/low), or machine learning classifiers trained on relevant features.
  2. **Regime-Specific Modeling:** Fit different StatsForecast models tailored to each identified regime or adjust model parameters based on the regime. For example, a more aggressive trend model during a bull regime and a more conservative or mean-reverting model during a bear or choppy regime.
  3. **Forecasting:** Use the model corresponding to the currently predicted regime to generate forecasts. An LLM agent would need to orchestrate this complex workflow, integrating regime detection logic with calls to StatsForecast for model fitting and prediction within each regime.

### **Combining GARCH with ARIMA/ETS for Point and Volatility Forecasts**

A widely adopted and effective strategy in financial forecasting is to model the conditional mean and conditional variance of returns separately. This is highly relevant for Bitcoin, which exhibits both price trends/patterns and time-varying volatility.

* **Two-Stage Process:**
  1. **Mean Modeling:** Fit an ARIMA or ETS model to the Bitcoin returns (often log returns) to capture serial correlation and generate point forecasts for future returns.14
  2. **Volatility Modeling:** Extract the residuals from the fitted mean model. These residuals represent the "unexplained" part of the returns. Fit a GARCH model (e.g., statsforecast.models.GARCH) to these residuals to model their conditional volatility.14 The GARCH model will then provide forecasts for future volatility.
* **LLM Script Generation:** The LLM agent should be capable of generating scripts that implement this two-stage procedure: first fitting an AutoARIMA or AutoETS model, obtaining its residuals (typically y\_true \- y\_pred\_insample), and then fitting a GARCH model to these residuals.

### **Using MLForecast for feature engineering as exogenous inputs to StatsForecast**

The Nixtla ecosystem includes MLForecast, a library for time series forecasting using machine learning models. MLForecast excels at creating a rich set of features from time series data, including various types of lags, rolling statistics (e.g., RollingMean, ExpandingStd), and date-based features.92

* **Synergy:** These features engineered by MLForecast can be highly valuable as exogenous variables for StatsForecast models. For example, one could compute rolling volatility, moving average crossovers, or other complex technical indicators using MLForecast's transformation capabilities and then feed these as X\_df into an AutoARIMA model in StatsForecast.
* **Workflow:**
  1. Use MLForecast's preprocessing capabilities (e.g., lag\_transforms, date\_features arguments in MLForecast class or standalone transformation functions) to generate desired features from the Bitcoin data.
  2. Merge these generated features with the original time series DataFrame.
  3. Pass this augmented DataFrame (containing y and the new exogenous features) to StatsForecast for model fitting and prediction. This hybrid approach allows leveraging the sophisticated feature engineering of machine learning frameworks while still utilizing the interpretable and often robust statistical models from StatsForecast. The LLM agent could script this workflow, enabling users to combine the strengths of both libraries.

### **Using StatsForecast as a baseline for NeuralForecast**

Nixtla also offers NeuralForecast for deep learning-based time series forecasting. When developing complex neural network models, it is crucial to establish strong baselines using simpler statistical or machine learning methods.

* StatsForecast models are excellent candidates for creating these baselines due to their speed and the variety of classical methods available.24
* An LLM agent could generate scripts that first run a selection of StatsForecast models (e.g., Naive, SeasonalNaive, AutoARIMA, AutoETS) to establish benchmark performance metrics before proceeding to train more complex models from NeuralForecast. This ensures that any added complexity from deep learning is justified by a significant improvement in forecast accuracy.

## **9\. Data Handling and Preprocessing for Bitcoin Data**

Effective forecasting heavily relies on appropriate data preparation. For Bitcoin data, which is often high-frequency and can contain various idiosyncrasies, preprocessing is a critical step.

### **Input Data Format: Pandas/Polars DataFrame (unique\_id, ds, y)**

StatsForecast is designed to work with time series data structured in a "long" DataFrame format, whether using Pandas or Polars.17 This format requires at least three specific columns:

* unique\_id: An identifier for each time series (e.g., 'BTCUSD', 'ETHUSD' if handling multiple cryptocurrencies).
* ds: The datestamp column, which should contain datetime objects or integer indices representing time.
* y: The target variable to be forecasted (e.g., Bitcoin closing price). Exogenous variables are included as additional columns in this DataFrame.

StatsForecast can automatically dispatch computations to different backends (local, Spark, Dask, Ray) depending on the type of the input DataFrame.4 Support for Polars DataFrames might require an explicit extra installation (e.g., pip install 'statsforecast\[polars\]' 147). Polars is recognized for its performance advantages, particularly with large datasets, due to its Apache Arrow-based in-memory format and built-in parallelism capabilities.131 However, it's important to note that Polars does not use a row index in the same way Pandas does, which can influence how certain data manipulations are performed.148 An LLM agent should be aware of Polars as a high-performance input option, especially if users report memory or speed issues with Pandas, and should also ensure the necessary dependencies are handled.

### **Handling Missing Data**

StatsForecast models generally expect complete, continuous time series data for the fitting period. Missing values in the target variable y or any exogenous variables can lead to errors or biased model estimates.17

* **Filling Timestamp Gaps:** The utilsforecast.preprocessing.fill\_gaps function is a utility provided within the Nixtla ecosystem to ensure a complete time index.17 It takes a DataFrame and a frequency (freq) and inserts rows for any missing timestamps, populating the target variable y and other data columns with NaN for these new rows.
* **Imputing Missing Target Values:** After ensuring a complete time index with fill\_gaps, the resulting NaN values in the y column (and any exogenous features) must be imputed. utilsforecast itself does not provide advanced imputation methods beyond fill\_gaps for index completion. Standard Pandas methods like interpolate() (linear, spline, etc.), fillna() (with mean, median, forward-fill, or backward-fill) are commonly used.151 The choice of imputation strategy is context-dependent: for Bitcoin prices, interpolation might be reasonable; for trading volumes where a missing value might genuinely mean zero volume on a specific exchange for a period, zero-filling could be appropriate. The LLM agent must be able to script this two-step process: first, index completion with fill\_gaps, and second, value imputation using appropriate methods.

### **Outlier Detection and Handling**

Bitcoin markets can experience extreme price movements or outliers due to various factors. While some StatsForecast models like CrostonOptimized 97 and MSTL (through its LOESS smoothing component 96) claim some degree of outlier robustness or smoothing capability, explicit outlier handling is often necessary for severe anomalies.

* The utilsforecast library does not appear to offer specialized outlier detection or treatment functions in the reviewed snippets.23
* Standard preprocessing techniques should be applied before model fitting:
  * **Detection:** Visual inspection of plots, statistical methods like Z-scores (identifying points several standard deviations from the mean), or IQR-based rules (e.g., values beyond Q1−1.5×IQR or Q3+1.5×IQR).98
  * **Treatment:** Options include removing the outlier (if it's clearly an error), winsorizing (capping the outlier at a certain percentile value), or replacing it with an imputed value.98 The LLM agent should be capable of suggesting or implementing these common outlier handling strategies as a preprocessing step for StatsForecast models when the data context (e.g., known periods of extreme market events for Bitcoin) warrants it.

### **Log Transformations vs. Level Forecasting for Bitcoin Prices**

Due to the typical exponential growth and increasing variance often seen in asset prices, including Bitcoin, log transformation is a common preprocessing step, particularly for ARIMA models.157

* **Log Transformation:** Applying y′=log(y) can help stabilize the variance and make the series' structure more amenable to linear models like ARIMA. Forecasts (y^​′) are then made on the transformed scale and subsequently inverse-transformed ($ \\hat{y} \= \\exp(\\hat{y}')$) to get predictions in the original units.
* **Handling Zeros:** If the data can contain zeros (e.g., trading volume), a common adjustment is to use log(y+c) (where c is a small constant, often 1\) to avoid issues with log(0). The back-transformation is then exp(y^​′)−c.157
* **Box-Cox Transformation:** AutoARIMA in StatsForecast supports Box-Cox transformations via the blambda parameter (which can be set to "auto" for automatic selection or a specific lambda value) and biasadj=True to adjust for median bias when back-transforming.6 This is a more general power transformation that includes log transformation as a special case (lambda=0).
* **Level Forecasting:** Some models may perform adequately on the original price levels, or transformations might complicate interpretation or introduce biases if not handled carefully. The choice depends on empirical validation. The LLM should be aware of these transformation options, particularly log transforms for price series and Box-Cox for AutoARIMA, and correctly implement the transformation and back-transformation steps.

### **Handling Multiple Exchanges / Granularities**

Bitcoin is traded across numerous exchanges, and data might be available at different time granularities.

* **Multiple Exchanges:** If forecasting Bitcoin prices from several exchanges, each exchange's price series can be treated as a distinct time series within StatsForecast by assigning a unique value in the unique\_id column.11 StatsForecast is inherently designed for such multi-series forecasting tasks.
* **Varying Granularities:** If input data (target variable or exogenous features) arrive at different frequencies (e.g., hourly Bitcoin prices but daily sentiment scores), a consistent modeling frequency must be chosen. This typically involves:
  * Aggregating higher-frequency data to match the lowest-frequency series (e.g., averaging hourly prices to get daily prices).
  * Forward-filling or repeating lower-frequency exogenous data to match higher-frequency target data.162 Careful consideration of the implications of such aggregation or disaggregation is necessary. The LLM should be able to generate code for these resampling and alignment tasks using Pandas/Polars functionalities.

## **10\. LLM Coding Agent Enablement: Generating Efficient StatsForecast Scripts**

The primary objective of this research is to equip an LLM coding agent with the knowledge to generate efficient and correct Python scripts utilizing the StatsForecast library for Bitcoin forecasting. This section outlines key patterns and considerations for the LLM.

### **Key StatsForecast Class and Model Instantiation Patterns**

The LLM must understand the fundamental instantiation pattern for the StatsForecast class and its models:

* **StatsForecast Class Instantiation:**
  Python
  from statsforecast import StatsForecast
  from statsforecast.models import AutoARIMA, Naive, GARCH \# etc.

  \# Define a list of instantiated model objects
  models\_to\_fit \=

  \# Instantiate StatsForecast
  sf \= StatsForecast(
      models=models\_to\_fit,
      freq='D',  \# Frequency of the time series data (e.g., 'D' for daily)
      n\_jobs=-1, \# Use all available CPU cores for local parallel processing
      \# fallback\_model=Naive() \# Optional: model to use if a primary model fails
  )
  Key parameters for the LLM to manage are models (a list of model instances), freq (which must match the input data's time frequency), and n\_jobs (for CPU utilization control).1
* **Model Instantiation:** Each model in the models list must be an instance of a class from statsforecast.models, configured with its specific parameters (e.g., AutoARIMA(season\_length=7), GARCH(p=1, q=1)).

### **Script Structure for Training, Prediction, and Cross-Validation**

The LLM should generate scripts following these common operational flows:

* **Training a Model:**
  Python
  \# Assuming 'train\_df' is a pandas/polars DataFrame with 'unique\_id', 'ds', 'y'
  \# and 'sf' is an instantiated StatsForecast object
  sf.fit(train\_df)
  \# Fitted models are stored in sf.fitted\_ if introspection is needed
  1
* **Making Predictions (after fit):**
  Python
  \# 'h' is the forecast horizon
  \# 'future\_exog\_df' (optional) contains future values of exogenous variables
  \# 'level' (optional) for prediction intervals
  predictions\_df \= sf.predict(h=10, X\_df=future\_exog\_df, level=)
  18
* **Memory-Efficient Forecasting (Combined Fit and Predict):**
  Python
  \# 'input\_df' contains historical data for fitting
  \# This method does not store fitted models to save memory
  forecast\_df \= sf.forecast(df=input\_df, h=10, X\_df=future\_exog\_df, level=)
  20
* **Cross-Validation:**
  Python
  \# 'cv\_df' will contain out-of-sample forecasts from multiple windows
  cv\_df \= sf.cross\_validation(
      df=train\_df,
      h=10, \# Forecast horizon for each window
      n\_windows=5, \# Number of validation windows
      step\_size=1, \# Steps between the start of each window
      \# test\_size=..., \# Alternative to n\_windows
      \# input\_size=..., \# For rolling windows
      refit=True, \# Whether to refit model for each window
      level=, \# Prediction intervals for CV forecasts
      \# fitted=True \# To store in-sample predictions from CV
  )
  1

### **Parameter Mapping for LLM: Translating Natural Language Requests**

The LLM needs a robust mapping from natural language user queries to specific StatsForecast model instantiations and parameter settings. For example:

* "Forecast daily Bitcoin price with weekly seasonality using ARIMA" → AutoARIMA(season\_length=7) or ARIMA(order=(p,d,q), seasonal\_order=(P,D,Q,7)).
* "Model Bitcoin volatility" → GARCH(p=1, q=1).
* "Include trading volume as an external factor" → Prepare DataFrame with volume column, pass as X\_df.
* "Perform automatic ARIMA selection" → Use AutoARIMA.
* "Prioritize a simpler ARIMA model" → AutoARIMA(ic='bic', stepwise=True, max\_order=small\_value).

### **Accessing Model Parameters and Residuals for Diagnostics**

For model validation and understanding, accessing fitted parameters and residuals is crucial.

* **Fitted Parameters:** After calling sf.fit(), the sf.fitted\_ attribute holds an array of fitted models. For a specific series (e.g., index s\_idx) and model (e.g., index m\_idx in the models list), parameters are often in sf.fitted\_\[s\_idx\]\[m\_idx\].model\_. This is a dictionary. For AutoARIMA, the selected orders (p,d,q)(P,D,Q,s) might be under the 'arma' key, and coefficients under 'coef'.34 The LLM can use this to extract parameters found by automatic methods (like AutoARIMA) and then instantiate a specific ARIMA model if needed for fine-tuning or deployment.
* **Residuals:**
  * Direct access to a .residuals\_ attribute on core StatsForecast models is not consistently documented across all models in the provided snippets.41
  * **Calculation from In-sample Forecasts:** The most reliable way appears to be:
    1. Obtain in-sample predictions:
       * Use sf.forecast(df=train\_df, h=horizon, fitted=True) then sf.forecast\_fitted\_values().
       * Or use sf.cross\_validation(df=train\_df, h=horizon, fitted=True) and then sf.cross\_validation\_fitted\_values().
       * If using sf.fit(), then sf.predict\_in\_sample(level=None) can retrieve in-sample predictions.41
    2. Calculate residuals: residuals \= y\_true\_insample \- y\_pred\_insample.
  * The LLM must be able to generate this calculation if a user requests residual diagnostics.
  * Some sktime wrappers around StatsForecast models do provide a predict\_residuals() method.47
* **Diagnostic Tests on Residuals:**
  * **Ljung-Box Test:** For checking autocorrelation in residuals. Use statsmodels.stats.diagnostic.acorr\_ljungbox(residuals, lags=\[...\]).125 The null hypothesis is that residuals are independently distributed (no autocorrelation).
  * **ARCH LM Test:** For checking for ARCH effects (conditional heteroskedasticity) in residuals. Use statsmodels.stats.diagnostic.het\_arch(residuals) or methods from the arch library if using its GARCH models.119 The null hypothesis is homoscedasticity (no ARCH effects).

### **Code Examples for Common Bitcoin Forecasting Tasks**

The LLM should be able to generate complete, runnable scripts for tasks like:

* **Example 1: Daily Bitcoin Price Forecast with AutoARIMA, Weekly Seasonality, and Holiday Effects**
  Python
  import pandas as pd
  from statsforecast import StatsForecast
  from statsforecast.models import AutoARIMA
  \# from nixtla.date\_features import CountryHolidays \# Assuming a similar utility or manual creation

  \# Sample Data (replace with actual Bitcoin data)
  \# unique\_id, ds (YYYY-MM-DD), y (price), US\_Holiday (dummy 0/1)
  data \= {
      'unique\_id': \* 100,
      'ds': pd.to\_datetime(pd.date\_range(start='2023-01-01', periods=100, freq='D')),
      'y': pd.np.random.rand(100) \* 10000 \+ 20000,
      'US\_Holiday': (\*5 \+  \+ \*93 \+ )\[:100\] \# Example holiday dummy
  }
  bitcoin\_df \= pd.DataFrame(data)

  \# Define future exogenous data (holidays for the forecast period)
  future\_dates \= pd.date\_range(start=bitcoin\_df\['ds'\].max() \+ pd.Timedelta(days=1), periods=14, freq='D')
  future\_exog\_df \= pd.DataFrame({
      'unique\_id': \* 14,
      'ds': future\_dates,
      'US\_Holiday': \*14 \# Placeholder, populate with actual future holiday data
  })

  sf \= StatsForecast(
      models=,
      freq='D',
      n\_jobs=-1
  )
  sf.fit(bitcoin\_df\])

  \# Exogenous variables for predict must match those in fit, excluding 'y'
  forecast \= sf.predict(h=14, X\_df=future\_exog\_df\])
  print(forecast.head())

* **Example 2: Hourly Bitcoin Log Return Volatility Forecast using GARCH(1,1)**
  Python
  import pandas as pd
  import numpy as np
  from statsforecast import StatsForecast
  from statsforecast.models import GARCH, AutoARIMA

  \# Sample Data (replace with actual hourly Bitcoin price data)
  data \= {
      'unique\_id': \* 200,
      'ds': pd.to\_datetime(pd.date\_range(start='2023-01-01', periods=200, freq='H')),
      'price': np.random.rand(200) \* 100 \+ 50000
  }
  bitcoin\_hourly\_df \= pd.DataFrame(data)
  bitcoin\_hourly\_df\['log\_returns'\] \= np.log(bitcoin\_hourly\_df\['price'\] / bitcoin\_hourly\_df\['price'\].shift(1))
  bitcoin\_hourly\_df.dropna(inplace=True)

  \# Prepare data for StatsForecast (target 'y' is log\_returns)
  returns\_df \= bitcoin\_hourly\_df\[\['unique\_id', 'ds'\]\].copy()
  returns\_df\['y'\] \= bitcoin\_hourly\_df\['log\_returns'\]

  \# Optional: Fit an ARIMA model to demean returns if necessary, then use residuals for GARCH
  \# For simplicity, directly modeling returns volatility here

  sf\_garch \= StatsForecast(
      models=,
      freq='H'
  )
  sf\_garch.fit(returns\_df)
  volatility\_forecast \= sf\_garch.predict(h=24) \# Forecast conditional variance for next 24 hours
  print(volatility\_forecast.head())

* **Example 3: Using MSTL with an AutoETS Trend Forecaster for Bitcoin Price Data**
  Python
  import pandas as pd
  from statsforecast import StatsForecast
  from statsforecast.models import MSTL, AutoETS

  \# Sample Data (replace with actual hourly Bitcoin price data)
  \# Assuming data has daily (24) and weekly (24\*7=168) seasonality
  data \= {
      'unique\_id': \* 500,
      'ds': pd.to\_datetime(pd.date\_range(start='2023-01-01', periods=500, freq='H')),
      'y': pd.np.random.rand(500) \* 1000 \+ 20000
  }
  bitcoin\_mstl\_df \= pd.DataFrame(data)

  sf\_mstl \= StatsForecast(
      models=, trend\_forecaster=AutoETS(model='ANN'))\], \# Example: daily & weekly
      freq='H'
  )
  sf\_mstl.fit(bitcoin\_mstl\_df)
  mstl\_forecast \= sf\_mstl.predict(h=48)
  print(mstl\_forecast.head())

The following table maps potential user intents to the script logic an LLM should generate:

**Table 7: Mapping User Intents to StatsForecast Script Logic for LLM**

| User Intent Example (Natural Language) | Key Information to Extract | Corresponding StatsForecast Model(s) | Key Parameters & Configuration | Exogenous Data Handling | Post-processing |
| :---- | :---- | :---- | :---- | :---- | :---- |
| "Forecast daily Bitcoin price with weekly seasonality." | Data frequency (daily), seasonality (weekly). | AutoARIMA or ARIMA | freq='D', season\_length=7 (for AutoARIMA), seasonal\_order=(P,D,Q,7) (for ARIMA). | None explicitly requested. | Plot forecast. |
| "Model Bitcoin volatility using GARCH." | Target (volatility), model family (GARCH). | GARCH | freq (matching returns data), p=1, q=1 (common default). | Input should be returns/residuals. | Plot conditional volatility forecast. |
| "Include trading volume and RSI as factors in my Bitcoin price forecast." | Exogenous variables (volume, RSI). | AutoARIMA (or other supporting model). | X\_df parameter in fit/predict. | Calculate volume, RSI; merge with target DataFrame; provide future values for prediction. | Evaluate feature importance if possible. |
| "Check if my ARIMA model's residuals are random." | Diagnostic task (residual check). | Fitted ARIMA or AutoARIMA model. |  |  | Access/calculate residuals. Perform Ljung-Box test. Plot ACF/PACF of residuals. |
| "Automatically select the best ETS model for my Bitcoin data." | Model family (ETS), automatic selection. | AutoETS | freq, season\_length (if applicable), model='ZZZ' (for full auto). | None explicitly requested. | Plot forecast, inspect chosen ETS components. |

## **11\. Production Considerations: Deployment and Monitoring**

Deploying forecasting models into production requires careful consideration of model persistence, experiment tracking, hyperparameter optimization, serving infrastructure, and ongoing monitoring.

### **Model Caching and Persistence**

* **Numba JIT Cache:** As discussed in Section 2, enabling Numba's caching mechanism via NIXTLA\_NUMBA\_CACHE=1 (and optionally NUMBA\_CACHE\_DIR) is crucial for production environments to avoid repeated JIT compilation overhead, ensuring consistent and fast model execution after the initial warm-up.25
* **Fitted Model Persistence:** After training, the StatsForecast object, which contains all the fitted models and their parameters, needs to be saved to disk for later use in inference without retraining. The joblib library is a common Python solution for serializing and deserializing complex Python objects, including scikit-learn and StatsForecast models.
  Python
  import joblib
  \# After sf.fit(train\_df)
  joblib.dump(sf, 'statsforecast\_bitcoin\_model.joblib')
  \# To load later:
  \# loaded\_sf \= joblib.load('statsforecast\_bitcoin\_model.joblib')
  This pattern is demonstrated in 33 and.182

### **Integration with MLflow: Experiment Tracking, Model Versioning**

MLflow is an open-source platform for managing the end-to-end machine learning lifecycle. The mlflavors library extends MLflow to provide native support for StatsForecast models.

* **Logging Models:** mlflavors.statsforecast.log\_model() allows logging the trained StatsForecast object, its parameters, performance metrics, and other artifacts (like plots or evaluation data) to an MLflow tracking server.163
  Python
  import mlflow
  import mlflavors
  \# Inside an MLflow run context:
  \# mlflow.log\_params({"season\_length": 7, "model\_type": "AutoARIMA"})
  \# mlflow.log\_metrics({"mae": validation\_mae, "rmse": validation\_rmse})
  \# mlflavors.statsforecast.log\_model(
  \#     statsforecast\_model=sf,
  \#     artifact\_path="bitcoin\_forecaster", \# Path within the run's artifacts
  \#     serialization\_format="pickle" \# or "cloudpickle"
  \# )
  \# model\_uri \= mlflow.get\_artifact\_uri("bitcoin\_forecaster")

* **Loading Models:** Logged models can be loaded back for inference using mlflavors.statsforecast.load\_model(model\_uri) or via the generic mlflow.pyfunc.load\_model(model\_uri) interface if logged with the PyFunc flavor.163 This integration facilitates experiment comparison, model versioning, reproducibility, and deployment management.

### **Hyperparameter Optimization with Optuna**

Optuna is a hyperparameter optimization framework that automates the search for optimal model parameters. It can be effectively used with StatsForecast models to tune parameters that are not automatically selected (e.g., season\_length in some cases, specific orders for manual ARIMA, or parameters of trend\_forecaster in MSTL).

* **Objective Function:** An objective function is defined that takes an Optuna trial object. Inside this function:
  1. Hyperparameters are suggested using trial.suggest\_categorical(), trial.suggest\_int(), trial.suggest\_float(), etc. (e.g., season\_length \= trial.suggest\_int('season\_length', 7, 28)).
  2. A StatsForecast model is instantiated and trained with these suggested hyperparameters.
  3. The model is evaluated (e.g., using StatsForecast.cross\_validation() and a chosen metric like MAE or RMSE).
  4. The evaluation metric is returned by the objective function.26
* **Study Optimization:** An Optuna study object is created (specifying direction='minimize' or 'maximize') and its optimize() method is called with the objective function and the number of trials.
  Python
  import optuna
  \# from statsforecast.losses import mae \# Example metric
  \# from sklearn.metrics import mean\_absolute\_error

  \# def objective(trial):
  \#     \# Suggest hyperparameters for AutoARIMA, for example
  \#     sl \= trial.suggest\_int('season\_length', 7, 30\)
  \#     ic\_choice \= trial.suggest\_categorical('ic', \['aic', 'aicc', 'bic'\])

  \#     model\_instance \= AutoARIMA(season\_length=sl, ic=ic\_choice)
  \#     sf\_opt \= StatsForecast(models=\[model\_instance\], freq='D')

  \#     \# Perform cross-validation or fit/predict on a validation set
  \#     \# cv\_results \= sf\_opt.cross\_validation(df=train\_data, h=forecast\_horizon, n\_windows=3)
  \#     \# error \= cv\_results\[model\_instance.alias\].mean() \# Example: average error
  \#     \# For simplicity, using a fixed validation set here
  \#     sf\_opt.fit(train\_data)
  \#     preds \= sf\_opt.predict(h=len(validation\_data))
  \#     error \= mean\_absolute\_error(validation\_data\['y'\], preds\[model\_instance.alias\])
  \#     return error

  \# study \= optuna.create\_study(direction='minimize')
  \# study.optimize(objective, n\_trials=50)
  \# best\_params \= study.best\_params
  \# print(f"Best parameters: {best\_params}")

This allows for systematic and automated tuning of StatsForecast models for optimal performance on Bitcoin data.

### **Serving Models (e.g., FastAPI, Docker)**

Once a satisfactory model is trained and persisted (e.g., as a .joblib file or an MLflow artifact), it can be deployed as a prediction service.

* **FastAPI for API Development:** FastAPI is a modern, high-performance Python web framework for building APIs. It can be used to create an endpoint that accepts input data (e.g., forecast horizon, future exogenous features if any) and returns predictions from the loaded StatsForecast model.180 Pydantic models are used within FastAPI to define request and response data schemas, ensuring data validation.
  Python
  \# main.py (FastAPI application)
  \# from fastapi import FastAPI
  \# from pydantic import BaseModel
  \# import joblib
  \# import pandas as pd

  \# app \= FastAPI()
  \# model \= joblib.load('statsforecast\_bitcoin\_model.joblib') \# Load persisted model

  \# class ForecastRequest(BaseModel):
  \#     horizon: int
  \#     \# Add fields for future\_exogenous\_data if needed
  \#     \# future\_exog: Optional\[list\[dict\]\] \= None

  \# @app.post("/predict\_bitcoin/")
  \# async def predict\_bitcoin(request: ForecastRequest):
  \#     \# Prepare X\_df if exogenous features are used
  \#     \# X\_df\_future \= pd.DataFrame(request.future\_exog) if request.future\_exog else None
  \#     forecast\_result \= model.predict(h=request.horizon) \#, X\_df=X\_df\_future)
  \#     return forecast\_result.to\_dict(orient='records')

* **Docker for Containerization:** The FastAPI application, along with the StatsForecast model, Python environment, and all dependencies, can be packaged into a Docker container.183 This ensures consistency across development, testing, and production environments and simplifies deployment. A Dockerfile would specify the base Python image, copy application code and the model artifact, install dependencies from requirements.txt, and define the command to run the FastAPI application using an ASGI server like Uvicorn.
  Dockerfile
  \# Dockerfile
  \# FROM python:3.9-slim
  \# WORKDIR /app
  \# COPY requirements.txt requirements.txt
  \# RUN pip install \--no-cache-dir \-r requirements.txt
  \# COPY..
  \# CMD \["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "80"\]
  184

### **Monitoring with Prometheus/Grafana (Conceptual)**

For production systems, monitoring model performance and service health is essential.

* **Prometheus:** An open-source monitoring and alerting toolkit. FastAPI applications can be instrumented (e.g., using starlette-prometheus or prometheus-fastapi-instrumentator 185) to expose metrics such as request rates, error rates, prediction latency, and potentially data drift or forecast accuracy metrics if calculated.
* **Grafana:** An open-source platform for visualization and analytics. Grafana can connect to Prometheus as a data source to create dashboards for visualizing the metrics exposed by the FastAPI service and the underlying StatsForecast model's behavior over time.185 This setup allows for real-time monitoring of the deployed Bitcoin forecasting service, enabling proactive issue detection and performance tracking.

## **12\. Conclusion**

StatsForecast emerges as a powerful and efficient Python framework for univariate time series forecasting, offering compelling advantages for production-grade Bitcoin price prediction. Its core strengths lie in the high-performance implementations of a wide range of statistical models, Numba JIT compilation for speed, and seamless integration with distributed computing backends like Dask and Ray. These features make it well-suited to handle the large volumes and high-frequency nature of cryptocurrency market data.

For an LLM coding agent, StatsForecast presents a structured and relatively consistent API. The standardized input data format (unique\_id, ds, y) and the common fit/predict/forecast patterns simplify script generation. Key areas where the LLM must demonstrate nuanced understanding include:

1. **Model Selection:** Guiding users or automatically choosing between general-purpose models (e.g., AutoARIMA, AutoETS) and specialized models (e.g., MSTL for multiple seasonalities, GARCH for volatility, Croston for truly intermittent data) based on data characteristics and forecasting objectives.
2. **Parameterization:** Correctly setting crucial parameters like season\_length, freq, ARIMA orders, ETS components, GARCH orders, and configuring search bounds or information criteria for automatic models.
3. **Exogenous Variables:** Generating scripts that not only incorporate user-provided exogenous data but also potentially engineer relevant features such as technical indicators, calendar dummies (especially for Bitcoin's 24/7 nature), or Fourier terms for complex seasonality.
4. **Performance Optimization:** Advising on or implementing Numba caching, selecting appropriate n\_jobs for local vs. distributed execution, and choosing between memory-efficient forecast() vs. inspectable fit().predict() methods.
5. **Diagnostics and Validation:** Generating code to access or calculate model residuals and perform diagnostic tests (Ljung-Box, ARCH LM) to validate model adequacy. Implementing robust cross-validation using StatsForecast.cross\_validation for performance evaluation.
6. **Advanced Strategies:** Facilitating hybrid approaches, such as combining ARIMA/ETS with GARCH for mean and volatility forecasting, or using MLForecast for feature engineering to enhance StatsForecast models.

While StatsForecast provides strong foundations, areas like advanced GARCH variants (EGARCH, GJR-GARCH), built-in ARFIMA, and automated structural break detection are not explicitly covered in its core offerings based on the reviewed materials. For these, the LLM may need to integrate StatsForecast with other specialized libraries (arch, ruptures) or implement custom logic.

The integration with MLOps tools like MLflow (via mlflavors) and hyperparameter optimization frameworks like Optuna further enhances StatsForecast's suitability for production environments, enabling systematic experimentation, model versioning, and robust deployment pipelines. By leveraging the detailed technical insights provided in this report, an LLM coding agent can be effectively enabled to generate efficient, accurate, and context-aware Python scripts for sophisticated Bitcoin forecasting using the Nixtla StatsForecast framework.

#### **Works cited**

1. StatsForecast ⚡️ \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/index.html](https://nixtlaverse.nixtla.io/statsforecast/index.html)
2. Statsforecast Alternatives \- Python Science and Data Analysis | LibHunt, accessed May 24, 2025, [https://python.libhunt.com/statsforecast-alternatives](https://python.libhunt.com/statsforecast-alternatives)
3. statsforecast \- PyPI, accessed May 24, 2025, [https://pypi.org/project/statsforecast/](https://pypi.org/project/statsforecast/)
4. Dask \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/distributed/dask.html](https://nixtlaverse.nixtla.io/statsforecast/docs/distributed/dask.html)
5. Statistical ⚡️ Forecast \- statsforecast · PyPI, accessed May 24, 2025, [https://pypi.org/project/statsforecast/0.5.6/](https://pypi.org/project/statsforecast/0.5.6/)
6. AutoARIMA Comparison (Prophet and pmdarima) \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/experiments/autoarima\_vs\_prophet.html](https://nixtlaverse.nixtla.io/statsforecast/docs/experiments/autoarima_vs_prophet.html)
7. Fast Time Series Forecasting with StatsForecast \- Towards Data Science, accessed May 24, 2025, [https://towardsdatascience.com/fast-time-series-forecasting-with-statsforecast-694d1670a2f3/](https://towardsdatascience.com/fast-time-series-forecasting-with-statsforecast-694d1670a2f3/)
8. Bitcoin price prediction \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/nixtla/docs/use-cases/bitcoin\_price\_prediction.html](https://nixtlaverse.nixtla.io/nixtla/docs/use-cases/bitcoin_price_prediction.html)
9. Bitcoin Price Prediction \- TimeGPT Foundational model for time series forecasting and anomaly detection \- Nixtla, accessed May 24, 2025, [https://www.nixtla.io/docs/use-cases-bitcoin\_price\_prediction](https://www.nixtla.io/docs/use-cases-bitcoin_price_prediction)
10. Bitcoin price prediction \- TimeGPT \- Nixtla, accessed May 24, 2025, [https://docs.nixtla.io/docs/use-cases-bitcoin\_price\_prediction](https://docs.nixtla.io/docs/use-cases-bitcoin_price_prediction)
11. Temporal Attention-Enhanced Stacking Networks: Revolutionizing Multi-Step Bitcoin Forecasting \- MDPI, accessed May 24, 2025, [https://www.mdpi.com/2571-9394/7/1/2](https://www.mdpi.com/2571-9394/7/1/2)
12. StatsForecast's Models \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/src/core/models\_intro](https://nixtlaverse.nixtla.io/statsforecast/src/core/models_intro)
13. Volatility forecasting (GARCH & ARCH) \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/garch\_tutorial.html](https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/garch_tutorial.html)
14. GARCH Model \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/garch.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/garch.html)
15. Holidays and special dates \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/nixtla/docs/tutorials/holidays.html](https://nixtlaverse.nixtla.io/nixtla/docs/tutorials/holidays.html)
16. Cross-validation \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/nixtla/docs/tutorials/cross\_validation.html](https://nixtlaverse.nixtla.io/nixtla/docs/tutorials/cross_validation.html)
17. IMAPA Model \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/imapa.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/imapa.html)
18. Quick Start \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/getting\_started\_short.html](https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/getting_started_short.html)
19. statsforecast/nbs/docs/tutorials/ElectricityPeakForecasting.ipynb at main \- GitHub, accessed May 24, 2025, [https://github.com/Nixtla/statsforecast/blob/main/nbs/docs/tutorials/ElectricityPeakForecasting.ipynb](https://github.com/Nixtla/statsforecast/blob/main/nbs/docs/tutorials/ElectricityPeakForecasting.ipynb)
20. Core Methods \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/src/core/core.html](https://nixtlaverse.nixtla.io/statsforecast/src/core/core.html)
21. Multiple seasonalities \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/multipleseasonalities.html](https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/multipleseasonalities.html)
22. Automatic Time Series Forecasting \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/how-to-guides/automatic\_forecasting.html](https://nixtlaverse.nixtla.io/statsforecast/docs/how-to-guides/automatic_forecasting.html)
23. End to End Walkthrough \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/getting\_started\_complete.html](https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/getting_started_complete.html)
24. Statistical, Machine Learning and Neural Forecasting methods \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/neuralforecast/docs/tutorials/comparing\_methods.html](https://nixtlaverse.nixtla.io/neuralforecast/docs/tutorials/comparing_methods.html)
25. Numba caching \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/how-to-guides/numba\_cache.html](https://nixtlaverse.nixtla.io/statsforecast/docs/how-to-guides/numba_cache.html)
26. Source code for autogluon.timeseries.models.local.statsforecast, accessed May 24, 2025, [https://auto.gluon.ai/stable/\_modules/autogluon/timeseries/models/local/statsforecast.html](https://auto.gluon.ai/stable/_modules/autogluon/timeseries/models/local/statsforecast.html)
27. Nixtla/statsforecast: Lightning ⚡️ fast forecasting with ... \- GitHub, accessed May 24, 2025, [https://github.com/Nixtla/statsforecast](https://github.com/Nixtla/statsforecast)
28. autogluon.timeseries.models.local.statsforecast, accessed May 24, 2025, [https://auto.gluon.ai/0.8.1/\_modules/autogluon/timeseries/models/local/statsforecast.html](https://auto.gluon.ai/0.8.1/_modules/autogluon/timeseries/models/local/statsforecast.html)
29. Input dataframe for statsforecast \#779 \- GitHub, accessed May 24, 2025, [https://github.com/Nixtla/statsforecast/discussions/779](https://github.com/Nixtla/statsforecast/discussions/779)
30. FugueBackend \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/src/core/distributed.fugue.html](https://nixtlaverse.nixtla.io/statsforecast/src/core/distributed.fugue.html)
31. Dynamic Optimized Theta Model \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/dynamicoptimizedtheta.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/dynamicoptimizedtheta.html)
32. Dynamic Standard Theta Model \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/dynamicstandardtheta.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/dynamicstandardtheta.html)
33. Feature request: method to save fitted models · Issue \#442 · Nixtla/statsforecast \- GitHub, accessed May 24, 2025, [https://github.com/Nixtla/statsforecast/issues/442](https://github.com/Nixtla/statsforecast/issues/442)
34. How to get model specification/paramters for models estimated with Nixtla's statsforecast package \- Stack Overflow, accessed May 24, 2025, [https://stackoverflow.com/questions/75290492/how-to-get-model-specification-paramters-for-models-estimated-with-nixtlas-stat](https://stackoverflow.com/questions/75290492/how-to-get-model-specification-paramters-for-models-estimated-with-nixtlas-stat)
35. Make best fitted ARIMA an output of AutoARIMA · Issue \#774 · Nixtla/statsforecast \- GitHub, accessed May 24, 2025, [https://github.com/Nixtla/statsforecast/issues/774](https://github.com/Nixtla/statsforecast/issues/774)
36. End to End Walkthrough with Polars \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/getting\_started\_complete\_polars.html](https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/getting_started_complete_polars.html)
37. Volatility forecasting (GARCH & ARCH) \- Colab, accessed May 24, 2025, [https://colab.research.google.com/github/Nixtla/statsforecast/blob/main/nbs/docs/tutorials/GARCH\_tutorial.ipynb](https://colab.research.google.com/github/Nixtla/statsforecast/blob/main/nbs/docs/tutorials/GARCH_tutorial.ipynb)
38. Forecasting Time Series \- Model Zoo \- AutoGluon 1.3.1 documentation, accessed May 24, 2025, [https://auto.gluon.ai/stable/tutorials/timeseries/forecasting-model-zoo.html](https://auto.gluon.ai/stable/tutorials/timeseries/forecasting-model-zoo.html)
39. Creating NumPy universal functions \- Numba, accessed May 24, 2025, [https://numba.pydata.org/numba-doc/dev/user/vectorize.html](https://numba.pydata.org/numba-doc/dev/user/vectorize.html)
40. 1.4. Creating Numpy universal functions \- Numba, accessed May 24, 2025, [https://numba.pydata.org/numba-doc/0.17.0/user/vectorize.html](https://numba.pydata.org/numba-doc/0.17.0/user/vectorize.html)
41. Models \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/src/core/models.html](https://nixtlaverse.nixtla.io/statsforecast/src/core/models.html)
42. AutoRegressive Model \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/autoregressive.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/autoregressive.html)
43. etna.models.StatsForecastARIMAModel — ETNA Time Series Library 2.7.1 documentation, accessed May 24, 2025, [https://docs.etna.ai/2.7.1/api\_reference/api/etna.models.StatsForecastARIMAModel.html](https://docs.etna.ai/2.7.1/api_reference/api/etna.models.StatsForecastARIMAModel.html)
44. ARIMA Model \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/arima.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/arima.html)
45. StatsForecastAutoARIMA — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.16.1/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://www.sktime.net/en/v0.16.1/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
46. StatsForecastAutoARIMA — sktime documentation \- Read the Docs, accessed May 24, 2025, [https://sktime-backup.readthedocs.io/en/v0.16.0/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://sktime-backup.readthedocs.io/en/v0.16.0/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
47. StatsForecastAutoARIMA \- aeon 0.8.1 documentation, accessed May 24, 2025, [https://www.aeon-toolkit.org/en/v0.8.1/api\_reference/auto\_generated/aeon.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://www.aeon-toolkit.org/en/v0.8.1/api_reference/auto_generated/aeon.forecasting.statsforecast.StatsForecastAutoARIMA.html)
48. AutoARIMA Model \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/autoarima.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/autoarima.html)
49. Source code for autogluon.timeseries.models.local.statsforecast, accessed May 24, 2025, [https://auto.gluon.ai/dev/\_modules/autogluon/timeseries/models/local/statsforecast.html](https://auto.gluon.ai/dev/_modules/autogluon/timeseries/models/local/statsforecast.html)
50. StatsForecastAutoARIMA — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.29.0/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://www.sktime.net/en/v0.29.0/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
51. Why TimeGPT? \- TimeGPT Foundational model for time series forecasting and anomaly detection \- Nixtla, accessed May 24, 2025, [https://www.nixtla.io/docs/getting-started-why\_timegpt\_](https://www.nixtla.io/docs/getting-started-why_timegpt_)
52. StatsForecastAutoARIMA — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.22.0/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://www.sktime.net/en/v0.22.0/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
53. nixtlaverse.nixtla.io, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/models/autoarima.html](https://nixtlaverse.nixtla.io/statsforecast/models/autoarima.html)
54. StatsForecastAutoARIMA — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.17.2/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://www.sktime.net/en/v0.17.2/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
55. StatsForecastAutoARIMA — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.13.1/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://www.sktime.net/en/v0.13.1/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
56. StatsForecastAutoARIMA — sktime documentation \- Read the Docs, accessed May 24, 2025, [https://sktime-backup.readthedocs.io/en/v0.26.1/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://sktime-backup.readthedocs.io/en/v0.26.1/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
57. StatsForecastAutoARIMA — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.27.0/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://www.sktime.net/en/v0.27.0/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
58. StatsForecastAutoARIMA — sktime documentation \- Read the Docs, accessed May 24, 2025, [https://sktime-backup.readthedocs.io/en/v0.13.2/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://sktime-backup.readthedocs.io/en/v0.13.2/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
59. StatsForecastAutoARIMA — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/latest/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html](https://www.sktime.net/en/latest/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoARIMA.html)
60. Exogenous Regressors \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/how-to-guides/exogenous.html](https://nixtlaverse.nixtla.io/statsforecast/docs/how-to-guides/exogenous.html)
61. 9 ARIMA models – Forecasting: Principles and Practice, the Pythonic Way \- OTexts, accessed May 24, 2025, [https://otexts.com/fpppy/nbs/09-arima.html](https://otexts.com/fpppy/nbs/09-arima.html)
62. 13 Some practical forecasting issues \- OTexts, accessed May 24, 2025, [https://otexts.com/fpppy/nbs/13-practical.html](https://otexts.com/fpppy/nbs/13-practical.html)
63. Unlocking Complex Seasonal Patterns: SARIMA Forecasting Techniques Explained, accessed May 24, 2025, [https://www.numberanalytics.com/blog/unlocking-complex-seasonal-patterns-sarima-techniques-explained](https://www.numberanalytics.com/blog/unlocking-complex-seasonal-patterns-sarima-techniques-explained)
64. ARIMAX in Practice: A Step-by-Step Tutorial \- Number Analytics, accessed May 24, 2025, [https://www.numberanalytics.com/blog/arimax-step-by-step-tutorial](https://www.numberanalytics.com/blog/arimax-step-by-step-tutorial)
65. \[2407.11786\] Cryptocurrency Price Forecasting Using XGBoost Regressor and Technical Indicators \- arXiv, accessed May 24, 2025, [https://arxiv.org/abs/2407.11786](https://arxiv.org/abs/2407.11786)
66. 10 Best Indicators for Crypto Trading in 2025 That Every Trader Must Know \- KoinX, accessed May 24, 2025, [https://www.koinx.com/blog/best-indicators-for-crypto-trading](https://www.koinx.com/blog/best-indicators-for-crypto-trading)
67. Microstructure and Market Dynamics in Crypto Markets David Easley, Maureen O'Hara, Songshan Yang , and Zhibai Zhang\* April, 2 \- Cornell University, accessed May 24, 2025, [https://stoye.economics.cornell.edu/docs/Easley\_ssrn-4814346.pdf](https://stoye.economics.cornell.edu/docs/Easley_ssrn-4814346.pdf)
68. Remove Holidays and Weekends in a very long time-serie, how to model time-series in Python? \- Stack Overflow, accessed May 24, 2025, [https://stackoverflow.com/questions/7653420/remove-holidays-and-weekends-in-a-very-long-time-serie-how-to-model-time-series](https://stackoverflow.com/questions/7653420/remove-holidays-and-weekends-in-a-very-long-time-serie-how-to-model-time-series)
69. 10 Dynamic regression models – Forecasting \- OTexts, accessed May 24, 2025, [https://otexts.com/fpppy/nbs/10-dynamic-regression.html](https://otexts.com/fpppy/nbs/10-dynamic-regression.html)
70. Creating Dummy Variables for Weekend and Holiday days \- Statalist, accessed May 24, 2025, [https://www.statalist.org/forums/forum/general-stata-discussion/general/1745831-creating-dummy-variables-for-weekend-and-holiday-days](https://www.statalist.org/forums/forum/general-stata-discussion/general/1745831-creating-dummy-variables-for-weekend-and-holiday-days)
71. 12 Advanced forecasting methods – Forecasting: Principles and ..., accessed May 24, 2025, [https://otexts.com/fpppy/nbs/12-advanced.html](https://otexts.com/fpppy/nbs/12-advanced.html)
72. AIC for ARIMA model · Nixtla statsforecast · Discussion \#922 \- GitHub, accessed May 24, 2025, [https://github.com/Nixtla/statsforecast/discussions/922](https://github.com/Nixtla/statsforecast/discussions/922)
73. Backtesting forecaster \- Skforecast Docs, accessed May 24, 2025, [https://skforecast.org/0.11.0/user\_guides/backtesting](https://skforecast.org/0.11.0/user_guides/backtesting)
74. Structural Breaks in Time Series Analysis: Managing Sudden Changes \- maseconomics, accessed May 24, 2025, [https://maseconomics.com/structural-breaks-in-time-series-analysis-managing-sudden-changes/](https://maseconomics.com/structural-breaks-in-time-series-analysis-managing-sudden-changes/)
75. nixtlaverse.nixtla.io, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/models.html](https://nixtlaverse.nixtla.io/statsforecast/models.html)
76. Ruptures \- CRC MINES ParisTech, accessed May 24, 2025, [https://www.crc.mines-paristech.fr/wp-content/uploads/2021/01/Notebook\_Ruptures.html](https://www.crc.mines-paristech.fr/wp-content/uploads/2021/01/Notebook_Ruptures.html)
77. Time series inflection point detection (changepoints detection) algorithm \- February 21, 2024 | Kaggle, accessed May 24, 2025, [https://www.kaggle.com/discussions/general/478555](https://www.kaggle.com/discussions/general/478555)
78. Python time-series analysis package: Statsmodels vs StatsForecast? \- Reddit, accessed May 24, 2025, [https://www.reddit.com/r/econometrics/comments/1j6cle8/python\_timeseries\_analysis\_package\_statsmodels\_vs/](https://www.reddit.com/r/econometrics/comments/1j6cle8/python_timeseries_analysis_package_statsmodels_vs/)
79. 13 Fractional Differencing and Threshold Models – STAT 510 | Applied Time Series Analysis, accessed May 24, 2025, [https://online.stat.psu.edu/stat510/Lesson13](https://online.stat.psu.edu/stat510/Lesson13)
80. Lesson 13: Fractional Differencing and Threshold Models \- STAT ONLINE, accessed May 24, 2025, [https://online.stat.psu.edu/stat510/book/export/html/674](https://online.stat.psu.edu/stat510/book/export/html/674)
81. Autoregressive fractionally integrated moving average \- Wikipedia, accessed May 24, 2025, [https://en.wikipedia.org/wiki/Autoregressive\_fractionally\_integrated\_moving\_average](https://en.wikipedia.org/wiki/Autoregressive_fractionally_integrated_moving_average)
82. StatsForecastAutoETS — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.37.0/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoETS.html](https://www.sktime.net/en/v0.37.0/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoETS.html)
83. StatsForecastAutoETS — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.25.1/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoETS.html](https://www.sktime.net/en/v0.25.1/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoETS.html)
84. arXiv:2504.00059v1 \[cs.LG\] 31 Mar 2025, accessed May 24, 2025, [https://arxiv.org/pdf/2504.00059](https://arxiv.org/pdf/2504.00059)
85. 8 Exponential smoothing – Forecasting: Principles and Practice, the Pythonic Way \- OTexts, accessed May 24, 2025, [https://otexts.com/fpppy/nbs/08-exponential-smoothing.html](https://otexts.com/fpppy/nbs/08-exponential-smoothing.html)
86. StatsForecastAutoETS — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.28.0/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoETS.html](https://www.sktime.net/en/v0.28.0/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoETS.html)
87. AutoETS — darts documentation, accessed May 24, 2025, [https://unit8co.github.io/darts/generated\_api/darts.models.forecasting.sf\_auto\_ets.html](https://unit8co.github.io/darts/generated_api/darts.models.forecasting.sf_auto_ets.html)
88. \[D\] Best Time Series models for Forecasting (alternative to TimeGPT)? : r/MachineLearning, accessed May 24, 2025, [https://www.reddit.com/r/MachineLearning/comments/193672o/d\_best\_time\_series\_models\_for\_forecasting/](https://www.reddit.com/r/MachineLearning/comments/193672o/d_best_time_series_models_for_forecasting/)
89. AutoTheta Model \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/autotheta.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/autotheta.html)
90. 4.1 ETS taxonomy | Forecasting and Analytics with the Augmented Dynamic Adaptive Model (ADAM), accessed May 24, 2025, [https://openforecast.org/adam/ETSTaxonomy.html](https://openforecast.org/adam/ETSTaxonomy.html)
91. 7.7 Forecasting with ETS models | Forecasting: Principles and Practice (2nd ed) \- OTexts, accessed May 24, 2025, [https://otexts.com/fpp2/ets-forecasting.html](https://otexts.com/fpp2/ets-forecasting.html)
92. StatsForecastAutoCES — sktime documentation \- Read the Docs, accessed May 24, 2025, [https://sktime-backup.readthedocs.io/en/stable/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoCES.html](https://sktime-backup.readthedocs.io/en/stable/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoCES.html)
93. 4.5 State space form of ETS | Forecasting and Analytics with ADAM, accessed May 24, 2025, [https://www.openforecast.org/adam/state-space-form-of-ets.html](https://www.openforecast.org/adam/state-space-form-of-ets.html)
94. StatsForecastMSTL — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/stable/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastMSTL.html](https://www.sktime.net/en/stable/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastMSTL.html)
95. StatsForecastMSTL — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.22.1/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastMSTL.html](https://www.sktime.net/en/v0.22.1/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastMSTL.html)
96. Multiple Seasonal Trend (MSTL) \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/multipleseasonaltrend.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/multipleseasonaltrend.html)
97. CrostonOptimized Model \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/crostonoptimized.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/crostonoptimized.html)
98. What preprocessing techniques are used in anomaly detection? \- Milvus, accessed May 24, 2025, [https://milvus.io/ai-quick-reference/what-preprocessing-techniques-are-used-in-anomaly-detection](https://milvus.io/ai-quick-reference/what-preprocessing-techniques-are-used-in-anomaly-detection)
99. Data Cleaning \- Dealing with Outliers \- Neural Data Science in Python, accessed May 24, 2025, [https://neuraldatascience.io/5-eda/data\_cleaning.html](https://neuraldatascience.io/5-eda/data_cleaning.html)
100. \[Guide\] Handling Outliers in Your Dataset \- Kaggle, accessed May 24, 2025, [https://www.kaggle.com/discussions/general/451887](https://www.kaggle.com/discussions/general/451887)
101. Probabilistic Forecasting \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/uncertaintyintervals.html](https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/uncertaintyintervals.html)
102. Conformal Prediction \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/conformalprediction.html](https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/conformalprediction.html)
103. Conformal prediction intervals insample data nixtla \- Stack Overflow, accessed May 24, 2025, [https://stackoverflow.com/questions/77578724/conformal-prediction-intervals-insample-data-nixtla](https://stackoverflow.com/questions/77578724/conformal-prediction-intervals-insample-data-nixtla)
104. Uncertainty quantification with Conformal Prediction \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/neuralforecast/docs/tutorials/conformal\_prediction.html](https://nixtlaverse.nixtla.io/neuralforecast/docs/tutorials/conformal_prediction.html)
105. A Comprehensive Guide to Conformal Prediction: Simplifying the Math, and Code, accessed May 24, 2025, [https://daniel-bethell.co.uk/posts/conformal-prediction-guide/](https://daniel-bethell.co.uk/posts/conformal-prediction-guide/)
106. GARCH model and statistical analysis on real financial data. \- GitHub, accessed May 24, 2025, [https://github.com/AdrienC21/garch-model-analysis](https://github.com/AdrienC21/garch-model-analysis)
107. StatsForecastAutoTheta — sktime documentation \- Read the Docs, accessed May 24, 2025, [https://sktime-backup.readthedocs.io/en/v0.21.1/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoTheta.html](https://sktime-backup.readthedocs.io/en/v0.21.1/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoTheta.html)
108. StatsForecastGARCH — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/stable/api\_reference/auto\_generated/sktime.forecasting.arch.StatsForecastGARCH.html](https://www.sktime.net/en/stable/api_reference/auto_generated/sktime.forecasting.arch.StatsForecastGARCH.html)
109. StatsForecastAutoCES — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.35.0/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoCES.html](https://www.sktime.net/en/v0.35.0/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoCES.html)
110. Cannot import several models with most recent version of package · Nixtla statsforecast · Discussion \#876 \- GitHub, accessed May 24, 2025, [https://github.com/Nixtla/statsforecast/discussions/876](https://github.com/Nixtla/statsforecast/discussions/876)
111. GARCH vs: ARCH: Understanding the Differences and Similarities \- FasterCapital, accessed May 24, 2025, [https://fastercapital.com/content/GARCH-vs--ARCH--Understanding-the-Differences-and-Similarities.html](https://fastercapital.com/content/GARCH-vs--ARCH--Understanding-the-Differences-and-Similarities.html)
112. GARCH Model: Definition and Uses in Statistics \- Investopedia, accessed May 24, 2025, [https://www.investopedia.com/terms/g/garch.asp](https://www.investopedia.com/terms/g/garch.asp)
113. Value at Risk estimation using GARCH model \- RPubs, accessed May 24, 2025, [https://rpubs.com/ionaskel/VaR\_Garch\_market\_risk](https://rpubs.com/ionaskel/VaR_Garch_market_risk)
114. Autoregressive conditional heteroskedasticity \- Wikipedia, accessed May 24, 2025, [https://en.wikipedia.org/wiki/Autoregressive\_conditional\_heteroskedasticity](https://en.wikipedia.org/wiki/Autoregressive_conditional_heteroskedasticity)
115. GARCH vs. GJR-GARCH Models in Python for Volatility Forecasting \- QuantInsti Blog, accessed May 24, 2025, [https://blog.quantinsti.com/garch-gjr-garch-volatility-forecasting-python/](https://blog.quantinsti.com/garch-gjr-garch-volatility-forecasting-python/)
116. GJR-GARCH Volatility Documentation \- V-Lab, accessed May 24, 2025, [https://vlab.stern.nyu.edu/docs/volatility/GJR-GARCH](https://vlab.stern.nyu.edu/docs/volatility/GJR-GARCH)
117. Can (G)ARCH models be applied when their is no MA component in the mean equation, or can only ARCH be applied? \- Cross Validated, accessed May 24, 2025, [https://stats.stackexchange.com/questions/338999/can-garch-models-be-applied-when-their-is-no-ma-component-in-the-mean-equation](https://stats.stackexchange.com/questions/338999/can-garch-models-be-applied-when-their-is-no-ma-component-in-the-mean-equation)
118. Simple Exponential Smoothing Model \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/simpleexponentialsmoothing.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/simpleexponentialsmoothing.html)
119. arch.univariate.base.ARCHModelResult.arch\_lm\_test \- arch 7.2.0, accessed May 24, 2025, [https://arch.readthedocs.io/en/stable/univariate/generated/generated/arch.univariate.base.ARCHModelResult.arch\_lm\_test.html](https://arch.readthedocs.io/en/stable/univariate/generated/generated/arch.univariate.base.ARCHModelResult.arch_lm_test.html)
120. Ultimate Guide to ARCH Models in Time Series, accessed May 24, 2025, [https://www.numberanalytics.com/blog/ultimate-guide-arch-models](https://www.numberanalytics.com/blog/ultimate-guide-arch-models)
121. GARCH Analysis on Volatility Patterns | EODHD APIs Academy \- EOD Historical Data, accessed May 24, 2025, [https://eodhd.com/financial-academy/stocks-data-analysis-examples/garch-analysis-on-volatility-patterns](https://eodhd.com/financial-academy/stocks-data-analysis-examples/garch-analysis-on-volatility-patterns)
122. Conditional Value at Risk using GARCH models \- Quantitative Finance Stack Exchange, accessed May 24, 2025, [https://quant.stackexchange.com/questions/69594/conditional-value-at-risk-using-garch-models](https://quant.stackexchange.com/questions/69594/conditional-value-at-risk-using-garch-models)
123. Conditional Value at Risk (CVar): Definition, Uses, Formula \- Investopedia, accessed May 24, 2025, [https://www.investopedia.com/terms/c/conditional\_value\_at\_risk.asp](https://www.investopedia.com/terms/c/conditional_value_at_risk.asp)
124. Forecasting VaR and CVaR based on a skewed exponential power mixture, in compliance with the new market risk regulation \- Chaire de recherche du Canada en gestion des risques \- HEC Montréal, accessed May 24, 2025, [https://chairegestiondesrisques.hec.ca/wp-content/uploads/2022/12/22-03.pdf](https://chairegestiondesrisques.hec.ca/wp-content/uploads/2022/12/22-03.pdf)
125. ARCH Model \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/arch.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/arch.html)
126. accessed December 31, 1969, [https://nixtlaverse.nixtla.io/statsforecast/models/mstl.html](https://nixtlaverse.nixtla.io/statsforecast/models/mstl.html)
127. Standard Theta Model \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/models/standardtheta.html](https://nixtlaverse.nixtla.io/statsforecast/docs/models/standardtheta.html)
128. StatsForecastAutoTheta — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/latest/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoTheta.html](https://www.sktime.net/en/latest/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoTheta.html)
129. accessed December 31, 1969, [https://nixtlaverse.nixtla.io/statsforecast/models/theta.html](https://nixtlaverse.nixtla.io/statsforecast/models/theta.html)
130. Changelog — darts documentation \- GitHub Pages, accessed May 24, 2025, [https://unit8co.github.io/darts/release\_notes/RELEASE\_NOTES.html](https://unit8co.github.io/darts/release_notes/RELEASE_NOTES.html)
131. Advanced prediction techniques for intermittent data \- m2hycon, accessed May 24, 2025, [https://www.m2hycon.de/en/news/leveraging-advanced-forecasting-techniques-with-statsforecast-a-case-study/](https://www.m2hycon.de/en/news/leveraging-advanced-forecasting-techniques-with-statsforecast-a-case-study/)
132. Croston — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/v0.35.1/api\_reference/auto\_generated/sktime.forecasting.croston.Croston.html](https://www.sktime.net/en/v0.35.1/api_reference/auto_generated/sktime.forecasting.croston.Croston.html)
133. accessed December 31, 1969, [https://nixtlaverse.nixtla.io/statsforecast/models/croston.html](https://nixtlaverse.nixtla.io/statsforecast/models/croston.html)
134. StatsForecastAutoTBATS — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/latest/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoTBATS.html](https://www.sktime.net/en/latest/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoTBATS.html)
135. StatsForecastAutoTBATS — sktime documentation, accessed May 24, 2025, [https://www.sktime.net/en/stable/api\_reference/auto\_generated/sktime.forecasting.statsforecast.StatsForecastAutoTBATS.html](https://www.sktime.net/en/stable/api_reference/auto_generated/sktime.forecasting.statsforecast.StatsForecastAutoTBATS.html)
136. nixtlaverse.nixtla.io, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/models/tbats.html](https://nixtlaverse.nixtla.io/statsforecast/models/tbats.html)
137. Model Combination, accessed May 24, 2025, [https://donskerclass.github.io/Forecasting/ModelCombination.html](https://donskerclass.github.io/Forecasting/ModelCombination.html)
138. On Regime Switching Models \- MDPI, accessed May 24, 2025, [https://www.mdpi.com/2227-7390/13/7/1128](https://www.mdpi.com/2227-7390/13/7/1128)
139. Regime–Switching Models, accessed May 24, 2025, [https://perhuaman.files.wordpress.com/2014/09/krolzig2002.pdf](https://perhuaman.files.wordpress.com/2014/09/krolzig2002.pdf)
140. Time Series Forecasting and Dynamic Asset Allocation: ARIMA and GARCH Models in Portfolio Management \- DiVA portal, accessed May 24, 2025, [http://www.diva-portal.org/smash/get/diva2:1895844/FULLTEXT01.pdf](http://www.diva-portal.org/smash/get/diva2:1895844/FULLTEXT01.pdf)
141. ARIMA+GARCH Trading Strategy on the S\&P500 Stock Market Index Using R | QuantStart, accessed May 24, 2025, [https://www.quantstart.com/articles/ARIMA-GARCH-Trading-Strategy-on-the-SP500-Stock-Market-Index-Using-R/](https://www.quantstart.com/articles/ARIMA-GARCH-Trading-Strategy-on-the-SP500-Stock-Market-Index-Using-R/)
142. Exogenous features \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/mlforecast/docs/how-to-guides/exogenous\_features.html](https://nixtlaverse.nixtla.io/mlforecast/docs/how-to-guides/exogenous_features.html)
143. How to MLForecast your Time Series Data\! \- Infinite Loop Digital, accessed May 24, 2025, [https://infiniteloopdigital.com/how-to-mlforecast-your-time-series-data/](https://infiniteloopdigital.com/how-to-mlforecast-your-time-series-data/)
144. Lag transformations \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/mlforecast/docs/how-to-guides/lag\_transforms\_guide.html](https://nixtlaverse.nixtla.io/mlforecast/docs/how-to-guides/lag_transforms_guide.html)
145. Introduction to NeuralForecast \- Packt+ | Advance your knowledge in tech, accessed May 24, 2025, [https://www.packtpub.com/en-EG/product/modern-time-series-forecasting-with-python-9781835883181/chapter/specialized-deep-learning-architectures-for-forecasting-19/section/introduction-to-neuralforecast-ch19lvl1sec38](https://www.packtpub.com/en-EG/product/modern-time-series-forecasting-with-python-9781835883181/chapter/specialized-deep-learning-architectures-for-forecasting-19/section/introduction-to-neuralforecast-ch19lvl1sec38)
146. nixtlaverse.nixtla.io, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/getting\_started\_polars.html](https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/getting_started_polars.html)
147. Install \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/installation.html](https://nixtlaverse.nixtla.io/statsforecast/docs/getting-started/installation.html)
148. Coming from Pandas \- Polars user guide, accessed May 24, 2025, [https://docs.pola.rs/user-guide/migration/pandas/](https://docs.pola.rs/user-guide/migration/pandas/)
149. Replacing Pandas with Polars. A Practical Guide. \- Python \- Reddit, accessed May 24, 2025, [https://www.reddit.com/r/Python/comments/10gf1cr/replacing\_pandas\_with\_polars\_a\_practical\_guide/](https://www.reddit.com/r/Python/comments/10gf1cr/replacing_pandas_with_polars_a_practical_guide/)
150. Missing Values \- TimeGPT Foundational model for time series forecasting and anomaly detection \- Nixtla, accessed May 24, 2025, [https://www.nixtla.io/docs/tutorials-special\_topics-tutorials-missing\_values](https://www.nixtla.io/docs/tutorials-special_topics-tutorials-missing_values)
151. Missing Values \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/nixtla/docs/tutorials/missing\_values.html](https://nixtlaverse.nixtla.io/nixtla/docs/tutorials/missing_values.html)
152. Data Requirements \- Nixtla \- Nixtlaverse, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/nixtla/docs/getting-started/data\_requirements.html](https://nixtlaverse.nixtla.io/nixtla/docs/getting-started/data_requirements.html)
153. Statistical, Machine Learning and Neural Forecasting methods \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/statisticalneuralmethods.html](https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/statisticalneuralmethods.html)
154. utilsforecast \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/utilsforecast/index.html](https://nixtlaverse.nixtla.io/utilsforecast/index.html)
155. Imputation in R: Top 3 Ways for Imputing Missing Data \- Appsilon, accessed May 24, 2025, [https://www.appsilon.com/post/imputation-in-r](https://www.appsilon.com/post/imputation-in-r)
156. Missing Data in Clinical Research: A Tutorial on Multiple Imputation \- PMC, accessed May 24, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC8499698/](https://pmc.ncbi.nlm.nih.gov/articles/PMC8499698/)
157. Forecasting Intermittent Demand \- TimeGPT \- Nixtla, accessed May 24, 2025, [https://docs.nixtla.io/docs/use-cases-forecasting\_intermittent\_demand](https://docs.nixtla.io/docs/use-cases-forecasting_intermittent_demand)
158. Bitcoin Short-term Price Prediction Using Time Series Analysis \- RIT Digital Institutional Repository, accessed May 24, 2025, [https://repository.rit.edu/cgi/viewcontent.cgi?article=12824\&context=theses](https://repository.rit.edu/cgi/viewcontent.cgi?article=12824&context=theses)
159. Time-series forecasting of Bitcoin prices using high-dimensional features: a machine learning approach \- PMC, accessed May 24, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC7334635/](https://pmc.ncbi.nlm.nih.gov/articles/PMC7334635/)
160. Looking for Tips on Forecasting Seasonal Inventory Data \- Cross Validated, accessed May 24, 2025, [https://stats.stackexchange.com/questions/663499/looking-for-tips-on-forecasting-seasonal-inventory-data](https://stats.stackexchange.com/questions/663499/looking-for-tips-on-forecasting-seasonal-inventory-data)
161. Top 7 Strategies for Effective Cryptocurrency Trading Applications \- Number Analytics, accessed May 24, 2025, [https://www.numberanalytics.com/blog/top-7-strategies-crypto-trading-applications](https://www.numberanalytics.com/blog/top-7-strategies-crypto-trading-applications)
162. Predicting a Time Series from Other Time Series and Continuous Predictors? \- Reddit, accessed May 24, 2025, [https://www.reddit.com/r/datascience/comments/1bos0zt/predicting\_a\_time\_series\_from\_other\_time\_series/](https://www.reddit.com/r/datascience/comments/1bos0zt/predicting_a_time_series_from_other_time_series/)
163. MLFlow \- Nixtla, accessed May 24, 2025, [https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/mlflow.html](https://nixtlaverse.nixtla.io/statsforecast/docs/tutorials/mlflow.html)
164. \[Models\]: summary method · Issue \#407 · Nixtla/statsforecast \- GitHub, accessed May 24, 2025, [https://github.com/Nixtla/statsforecast/issues/407](https://github.com/Nixtla/statsforecast/issues/407)
165. The Ultimate Guide to Ljung-Box Test for Analysts \- Number Analytics, accessed May 24, 2025, [https://www.numberanalytics.com/blog/ultimate-ljung-box-test-guide](https://www.numberanalytics.com/blog/ultimate-ljung-box-test-guide)
166. Ljung-Box test | Python, accessed May 24, 2025, [https://campus.datacamp.com/courses/garch-models-in-python/model-performance-evaluation?ex=8](https://campus.datacamp.com/courses/garch-models-in-python/model-performance-evaluation?ex=8)
167. Master Ljung-Box Test: Key Steps for Time Series \- Number Analytics, accessed May 24, 2025, [https://www.numberanalytics.com/blog/master-ljung-box-test-steps](https://www.numberanalytics.com/blog/master-ljung-box-test-steps)
168. A Deep Dive into Ultimate ARCH Models Research, accessed May 24, 2025, [https://www.numberanalytics.com/blog/ultimate-arch-models-econometrics-guide](https://www.numberanalytics.com/blog/ultimate-arch-models-econometrics-guide)
169. Source code for mlflavors.statsforecast, accessed May 24, 2025, [https://mlflavors.readthedocs.io/en/latest/\_modules/mlflavors/statsforecast.html](https://mlflavors.readthedocs.io/en/latest/_modules/mlflavors/statsforecast.html)
170. Log, load, and register MLflow models \- Databricks Documentation, accessed May 24, 2025, [https://docs.databricks.com/aws/en/mlflow/models](https://docs.databricks.com/aws/en/mlflow/models)
171. ml-toolkits/mlflavors: A collection of MLflow custom flavors \- GitHub, accessed May 24, 2025, [https://github.com/ml-toolkits/mlflavors](https://github.com/ml-toolkits/mlflavors)
172. accessed December 31, 1969, [https://nixtla.io/blog/mlflow-on-statsforecast/](https://nixtla.io/blog/mlflow-on-statsforecast/)
173. Optuna \- A hyperparameter optimization framework, accessed May 24, 2025, [https://optuna.org/](https://optuna.org/)
174. optuna/optuna: A hyperparameter optimization framework \- GitHub, accessed May 24, 2025, [https://github.com/optuna/optuna](https://github.com/optuna/optuna)
175. \[ENH\] Adding ForecastingOptunaSearchCV for hyper-parameter tuning for forecasting \#6618 \- GitHub, accessed May 24, 2025, [https://github.com/sktime/sktime/issues/6618](https://github.com/sktime/sktime/issues/6618)
176. Optuna: Hyperparameter Tuning \- YouTube, accessed May 24, 2025, [https://www.youtube.com/watch?v=2JuzqVbjSKU](https://www.youtube.com/watch?v=2JuzqVbjSKU)
177. accessed December 31, 1969, [https://nixtla.io/blog/](https://nixtla.io/blog/)
178. accessed December 31, 1969, [https://github.com/Nixtla/statsforecast/tree/main/nbs/docs/examples](https://github.com/Nixtla/statsforecast/tree/main/nbs/docs/examples)
179. Nixtla statsforecast · Discussions · GitHub, accessed May 24, 2025, [https://github.com/Nixtla/statsforecast/discussions](https://github.com/Nixtla/statsforecast/discussions)
180. Declare Request Example Data \- FastAPI, accessed May 24, 2025, [https://fastapi.tiangolo.com/tutorial/schema-extra-example/](https://fastapi.tiangolo.com/tutorial/schema-extra-example/)
181. Extra Models \- FastAPI, accessed May 24, 2025, [https://fastapi.tiangolo.com/tutorial/extra-models/](https://fastapi.tiangolo.com/tutorial/extra-models/)
182. Simplifying ML Model Integration with FastAPI \- CodeCut, accessed May 24, 2025, [https://codecut.ai/simplifying-ml-model-integration-with-fastapi/](https://codecut.ai/simplifying-ml-model-integration-with-fastapi/)
183. How does FastAPI utilize the CPU? \- Reddit, accessed May 24, 2025, [https://www.reddit.com/r/FastAPI/comments/1em6gwl/how\_does\_fastapi\_utilize\_the\_cpu/](https://www.reddit.com/r/FastAPI/comments/1em6gwl/how_does_fastapi_utilize_the_cpu/)
184. FastAPI in Containers \- Docker, accessed May 24, 2025, [https://fastapi.tiangolo.com/deployment/docker/](https://fastapi.tiangolo.com/deployment/docker/)
185. Kludex/fastapi-prometheus-grafana \- GitHub, accessed May 24, 2025, [https://github.com/Kludex/fastapi-prometheus-grafana](https://github.com/Kludex/fastapi-prometheus-grafana)
186. ARCH vs GARCH (The Background) \#garch \#arch \#clustering \#volatility \#mgarch \#tgarch \#egarch \#igarch \- YouTube, accessed May 24, 2025, [https://m.youtube.com/watch?v=B-fYft6b4nE](https://m.youtube.com/watch?v=B-fYft6b4nE)