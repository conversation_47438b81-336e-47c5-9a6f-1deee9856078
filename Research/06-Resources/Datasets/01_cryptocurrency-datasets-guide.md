---
title: "Cryptocurrency Data Standards for Time Series Forecasting"
permalink: "resources/datasets/cryptocurrency-data-standards"
type: "resource"
created: "2025-05-24"
last_updated: "2025-01-27"
tags:
  - data-standards
  - cryptocurrency
  - bitcoin
  - ethereum
  - data-format
  - preprocessing
models:
  - "All forecasting models"
libraries:
  - "pandas"
  - "numpy"
techniques:
  - "data-standardization"
  - "data-preprocessing"
  - "feature-engineering"
  - "data-validation"
datasets:
  - "Standard OHLCV format"
  - "Cryptocurrency time series"
complexity: "beginner"
summary: "Data-source agnostic guide defining standard cryptocurrency data formats and preprocessing pipelines for time series forecasting. Works with any data source providing the standard format."
related:
  - "techniques/feature-engineering"
  - "domain-applications/cryptocurrency/bitcoin/bitcoin-forecasting-guide"
  - "case-studies/benchmarks/nixtla-model-comparison-benchmark"
---

# Cryptocurrency Data Standards for Time Series Forecasting

## Overview

This guide defines data-source agnostic standards for cryptocurrency time series forecasting. All examples work with any data source that provides the standard format, whether from exchange APIs, databases, CSV files, or real-time streams.

## Core Principle: Data Source Agnostic Design

The forecasting system should work seamlessly regardless of where the data originates. This guide focuses on:

1. **Standard Data Format**: Consistent structure across all sources
2. **Preprocessing Pipeline**: Universal data preparation steps
3. **Validation Framework**: Quality assurance independent of source
4. **Feature Engineering**: Source-agnostic feature creation

## Standard Data Format

### Required DataFrame Structure

All data sources must provide data in this standardized format:

```python
import pandas as pd
import numpy as np

# Standard DataFrame structure for cryptocurrency time series
def create_standard_format():
    """
    Standard format that all data sources must conform to.
    Works with any source: APIs, databases, CSV files, streams.
    """
    return pd.DataFrame({
        'timestamp': pd.Timestamp,  # UTC timestamp
        'open': float,              # Opening price
        'high': float,              # Highest price in period
        'low': float,               # Lowest price in period
        'close': float,             # Closing price
        'volume': float,            # Trading volume
        # Optional additional columns:
        'market_cap': float,        # Market capitalization (optional)
        'trades': int,              # Number of trades (optional)
    }).set_index('timestamp')

# Example of properly formatted data
sample_data = pd.DataFrame({
    'timestamp': pd.date_range('2024-01-01', periods=100, freq='1H'),
    'open': np.random.uniform(40000, 50000, 100),
    'high': np.random.uniform(40000, 50000, 100),
    'low': np.random.uniform(40000, 50000, 100),
    'close': np.random.uniform(40000, 50000, 100),
    'volume': np.random.uniform(1000, 10000, 100)
}).set_index('timestamp')
```

### Data Source Adapter Pattern

Create adapters to convert any data source to the standard format:

```python
class DataSourceAdapter:
    """Base adapter for converting any data source to standard format"""

    def __init__(self, source_config):
        self.config = source_config

    def fetch_data(self, symbol, start_date, end_date, frequency):
        """Fetch data from source and convert to standard format"""
        raw_data = self._fetch_raw_data(symbol, start_date, end_date, frequency)
        return self._convert_to_standard(raw_data)

    def _fetch_raw_data(self, symbol, start_date, end_date, frequency):
        """Override in subclasses for specific data sources"""
        raise NotImplementedError

    def _convert_to_standard(self, raw_data):
        """Convert raw data to standard DataFrame format"""
        # Ensure required columns exist
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

        # Standardize column names (case-insensitive)
        raw_data.columns = [col.lower() for col in raw_data.columns]

        # Convert timestamp to datetime index
        if 'timestamp' in raw_data.columns:
            raw_data['timestamp'] = pd.to_datetime(raw_data['timestamp'])
            raw_data = raw_data.set_index('timestamp')

        # Ensure numeric types
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in raw_data.columns:
                raw_data[col] = pd.to_numeric(raw_data[col], errors='coerce')

        return raw_data

# Example adapters for different sources
class CSVAdapter(DataSourceAdapter):
    """Adapter for CSV files"""
    def _fetch_raw_data(self, file_path, **kwargs):
        return pd.read_csv(file_path)

class DatabaseAdapter(DataSourceAdapter):
    """Adapter for database connections"""
    def _fetch_raw_data(self, query, **kwargs):
        # Implementation depends on database type
        pass

class APIAdapter(DataSourceAdapter):
    """Adapter for REST APIs"""
    def _fetch_raw_data(self, endpoint, **kwargs):
        # Implementation depends on API specification
        pass
```

### Universal Data Loader

```python
def load_crypto_data(source_type, source_config, symbol, start_date, end_date, frequency='1H'):
    """
    Universal data loader that works with any source type.

    Parameters:
    - source_type: 'csv', 'database', 'api', 'stream'
    - source_config: Configuration dict for the specific source
    - symbol: Cryptocurrency symbol (e.g., 'BTC', 'ETH')
    - start_date: Start date for data
    - end_date: End date for data
    - frequency: Data frequency ('1m', '5m', '1H', '1D', etc.)

    Returns:
    - DataFrame in standard format
    """

    adapters = {
        'csv': CSVAdapter,
        'database': DatabaseAdapter,
        'api': APIAdapter,
        # Add more adapters as needed
    }

    if source_type not in adapters:
        raise ValueError(f"Unsupported source type: {source_type}")

    adapter = adapters[source_type](source_config)
    return adapter.fetch_data(symbol, start_date, end_date, frequency)

# Usage examples - same interface regardless of source
btc_from_csv = load_crypto_data(
    source_type='csv',
    source_config={'file_path': 'btc_data.csv'},
    symbol='BTC',
    start_date='2024-01-01',
    end_date='2024-12-31'
)

btc_from_api = load_crypto_data(
    source_type='api',
    source_config={'endpoint': 'https://api.example.com', 'api_key': 'key'},
    symbol='BTC',
    start_date='2024-01-01',
    end_date='2024-12-31'
)
```

## Dataset Characteristics

### Bitcoin (BTC) Datasets

#### Daily Data (2010-Present)
- **Size**: ~5,000 observations
- **Features**: OHLCV, market cap
- **Characteristics**: Long-term trends, lower volatility
- **Best for**: Long-term forecasting, trend analysis

#### Hourly Data (2017-Present)
- **Size**: ~60,000 observations
- **Features**: OHLCV, technical indicators
- **Characteristics**: High volatility, intraday patterns
- **Best for**: Short-term forecasting, trading strategies

#### Minute Data (Recent 2-3 years)
- **Size**: 1M+ observations
- **Features**: OHLCV, order book data
- **Characteristics**: Very high frequency, noise
- **Best for**: High-frequency trading, microstructure analysis

### Ethereum (ETH) Datasets

#### Key Differences from Bitcoin
- **DeFi Integration**: TVL, DEX volumes
- **Gas Fees**: Network congestion indicator
- **Staking Data**: Post-merge staking metrics
- **Developer Activity**: GitHub commits, active addresses

### Alternative Cryptocurrencies

#### High Market Cap (Top 10)
- More stable, better data quality
- Longer historical data available
- Good for comparative studies

#### Mid-Cap Cryptocurrencies
- Higher volatility
- Less historical data
- Interesting for volatility modeling

## Data Preprocessing Pipeline

### 1. Data Cleaning

```python
def clean_crypto_data(df):
    """Standard cleaning pipeline for crypto data"""
    # Remove duplicates
    df = df.drop_duplicates()

    # Handle missing values
    df = df.fillna(method='ffill').fillna(method='bfill')

    # Remove outliers (price spikes > 50% in 1 hour)
    df['price_change'] = df['close'].pct_change()
    outlier_mask = abs(df['price_change']) > 0.5
    df.loc[outlier_mask, 'close'] = df['close'].rolling(3, center=True).median()

    # Ensure positive prices
    df = df[df['close'] > 0]

    return df
```

### 2. Feature Engineering

```python
def engineer_crypto_features(df):
    """Create technical and fundamental features"""

    # Price-based features
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    df['price_ma_7'] = df['close'].rolling(7).mean()
    df['price_ma_30'] = df['close'].rolling(30).mean()

    # Volatility features
    df['volatility_24h'] = df['returns'].rolling(24).std()
    df['volatility_7d'] = df['returns'].rolling(168).std()

    # Volume features
    df['volume_ma_24h'] = df['volume'].rolling(24).mean()
    df['volume_ratio'] = df['volume'] / df['volume_ma_24h']

    # Technical indicators
    df['rsi'] = calculate_rsi(df['close'])
    df['macd'], df['macd_signal'] = calculate_macd(df['close'])
    df['bb_upper'], df['bb_lower'] = calculate_bollinger_bands(df['close'])

    # Time-based features
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['month'] = df.index.month

    return df

def calculate_rsi(prices, window=14):
    """Calculate Relative Strength Index"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))
```

### 3. Data Validation

```python
def validate_crypto_dataset(df):
    """Validate dataset quality"""
    validation_results = {}

    # Check for missing values
    validation_results['missing_values'] = df.isnull().sum().sum()

    # Check for price anomalies
    price_changes = df['close'].pct_change()
    validation_results['extreme_changes'] = (abs(price_changes) > 0.5).sum()

    # Check temporal consistency
    time_diffs = df.index.to_series().diff()
    expected_freq = time_diffs.mode()[0]
    validation_results['irregular_intervals'] = (time_diffs != expected_freq).sum()

    # Check volume consistency
    validation_results['zero_volume'] = (df['volume'] == 0).sum()

    return validation_results
```

## Recommended Data Specifications by Use Case

### 1. Academic Research

**Long-term Analysis Dataset**
- **Frequency**: Daily (1D)
- **Duration**: 5+ years historical data
- **Features**: Standard OHLCV + market cap
- **Size**: ~3,000-5,000 observations
- **Format**: Standard DataFrame with timestamp index
- **Good for**: Long-term trend analysis, academic papers

### 2. Trading Strategy Development

**Intraday Strategy Dataset**
- **Frequency**: Hourly (1H) or 4-hour (4H)
- **Duration**: 2-4 years historical data
- **Features**: Standard OHLCV + technical indicators
- **Size**: ~35,000-70,000 observations
- **Format**: Standard DataFrame with engineered features
- **Good for**: Backtesting, strategy validation

### 3. High-Frequency Analysis

**Microstructure Dataset**
- **Frequency**: Minute (1m) or sub-minute
- **Duration**: 3-12 months recent data
- **Features**: Standard OHLCV + order book data
- **Size**: 250,000+ observations
- **Format**: High-frequency DataFrame with microsecond precision
- **Good for**: Market microstructure, algorithmic trading

### 4. Multi-Asset Studies

**Cross-Asset Portfolio Dataset**
- **Frequency**: Daily (1D) or Hourly (1H)
- **Duration**: 3+ years synchronized data
- **Features**: Standard OHLCV for multiple assets
- **Size**: Variable based on asset count
- **Format**: Multi-symbol DataFrame or separate DataFrames
- **Good for**: Portfolio optimization, correlation analysis

## Data Quality Considerations

### Common Issues

1. **Exchange Downtime**: Missing data during outages
2. **Flash Crashes**: Extreme price movements
3. **Low Liquidity**: Unreliable prices for small coins
4. **Fork Events**: Price discontinuities
5. **Timezone Issues**: Inconsistent timestamps

### Quality Metrics

```python
def calculate_data_quality_score(df):
    """Calculate overall data quality score"""
    scores = {}

    # Completeness (0-100)
    scores['completeness'] = (1 - df.isnull().sum().sum() / df.size) * 100

    # Consistency (0-100)
    price_changes = df['close'].pct_change()
    extreme_changes = (abs(price_changes) > 0.5).sum()
    scores['consistency'] = max(0, 100 - extreme_changes / len(df) * 1000)

    # Timeliness (0-100)
    time_gaps = df.index.to_series().diff()
    expected_freq = time_gaps.mode()[0]
    irregular_count = (time_gaps != expected_freq).sum()
    scores['timeliness'] = max(0, 100 - irregular_count / len(df) * 100)

    # Overall score
    overall_score = sum(scores.values()) / len(scores)

    return scores, overall_score
```

## Best Practices for Data-Source Agnostic Systems

### 1. Data Standardization
- Define clear data format standards
- Implement adapter pattern for different sources
- Validate data structure consistency
- Document format specifications

### 2. Source-Agnostic Processing
- Design pipelines independent of data source
- Use universal data loaders
- Implement consistent validation frameworks
- Maintain temporal order regardless of source

### 3. Feature Engineering
- Create domain-specific features from standard format
- Avoid source-specific dependencies
- Validate feature importance across sources
- Consider computational cost

### 4. Quality Assurance
- Implement universal validation metrics
- Test with multiple data sources
- Monitor data quality continuously
- Document all transformations

## Conclusion

A data-source agnostic approach ensures robust cryptocurrency forecasting systems. Key principles include:

1. **Standard Format**: Define consistent data structure across all sources
2. **Adapter Pattern**: Convert any source to standard format seamlessly
3. **Universal Processing**: Design pipelines that work with any conforming data
4. **Quality Assurance**: Implement source-independent validation

This approach enables:
- **Flexibility**: Easy switching between data sources
- **Scalability**: Adding new sources without system changes
- **Reliability**: Consistent behavior regardless of data origin
- **Maintainability**: Single codebase for all data sources

All examples in this guide work with any data source that provides the standard format, whether from exchange APIs, databases, CSV files, or real-time streams.
